﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Microsoft.Extensions.Configuration;

namespace PBC.AggregatorService.Controllers
{
    public class SecurityController : Controller
    {               
     
        private readonly IConfiguration _configuration;
        public SecurityController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        #region JWT token Gen
        /// <summary>
        /// DK - 24-Jun-2023 - To generate JWT TOKEN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public string GenerateToken(string username)
        {
            var secretKey = _configuration["Jwt:SecretKey"];
            var issuer = _configuration["Jwt:Issuer"];
            var audience = _configuration["Jwt:Audience"];
            var expiryMinutes = int.Parse(_configuration["Jwt:ExpiryMinutes"]);

            var securityKey = new SymmetricSecurityKey(Convert.FromBase64String(secretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
            new Claim(JwtRegisteredClaimNames.Sub, username ?? ""),
            new Claim(JwtRegisteredClaimNames.Iss, issuer),
            new Claim(JwtRegisteredClaimNames.Aud, audience)
        };

            var now = DateTime.UtcNow;
            var expires = now.AddMinutes(expiryMinutes);

            var token = new JwtSecurityToken(
                issuer: issuer,
                audience: audience,
                claims: claims,
                notBefore: now,
                expires: expires,
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        #endregion

    }
}



//DK
//CLIENT SIDE CODE 
//$.ajax({
//url: 'https://epcwebapi.azurewebsites.net/api/JWT/JWTGenOnLogin',
//    type: 'POST',
//    data: JSON.stringify({ username: username }),
//    contentType: 'application/json',
//    dataType: 'json',
//    processData: false,
//    success: function(data) {
//        if (data && data.token && data.token.length > 0)
//        {
//            localStorage.setItem('jwtToken', data.token);
//            // Use the token as needed
//        }
//        else
//        {
//            console.error('Empty or invalid response content');
//        }
//    },
//    error: function(xhr, status, error) {
//        console.error('JWT request failed:', error);
//    }
//});