
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using PBC.HelpdeskService.Models;

namespace PBC.HelpdeskService.Services
{
    public class HelpDeskUserLandingPageServices : IHelpDeskUserLandingPageServices
    {
        private readonly IUtilityServiceClient _utilityServiceClient;
        private readonly ILogger<HelpDeskUserLandingPageServices> _logger;
        private readonly IConfiguration _configuration;

        public static string IsClosed = "";
        public static string LandingPageServiceRequestExport = "";
        public static int MaxStatusValue = 0;
        public static int Mode = 0;

        public HelpDeskUserLandingPageServices(IUtilityServiceClient utilityServiceClient, ILogger<HelpDeskUserLandingPageServices> logger, IConfiguration configuration)
        {
            _utilityServiceClient = utilityServiceClient;
            _logger = logger;
            _configuration = configuration;
        }
        #region ::: SelectDealerName :::
        /// <summary>
        /// To get Supplier Names
        /// </summary>
        /// <returns>...</returns>
        public async Task<IActionResult> SelectDealerNameAsync(SelectDealerNameList Obj, string connString, int LogException)
        {
            string value = await _utilityServiceClient.DecryptStringAsync(Obj.value);
            var jsonResult = default(dynamic);

            int Company_ID = Convert.ToInt32(Obj.Company_ID);
            int BranchID = Convert.ToInt32(Obj.Branch);
            GNM_Branch companyRow = null;
            List<GNM_Party> liSupplierDetails = new List<GNM_Party>();
            List<NameID> PartyAdd = new List<NameID>();
            try
            {

                string select = await _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "select");
                string Query = string.Empty;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"SELECT TOP 1 * 
                    FROM GNM_Branch 
                    WHERE LOWER(Branch_Name) = @BranchName AND Branch_ID != @BranchID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@BranchName", value.ToLower());
                            cmd.Parameters.AddWithValue("@BranchID", BranchID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    jsonResult = new
                                    {
                                        Result = "1",
                                        BelongsTo = "Party",
                                        Party_ID = reader["Branch_ID"],
                                        Mobile = reader["Branch_Mobile"],
                                        Email = reader["Branch_Email"],
                                        Party_IsLocked = false,
                                        Party_Name = reader["Branch_Name"],
                                        PartyAdd = PartyAdd,
                                        Party_IsActive = reader["Branch_Active"],
                                        CreditLimit = 0,
                                        CreditAmount = 0,
                                        IsDealer = true,
                                    };
                                }
                                else
                                {
                                    jsonResult = new
                                    {
                                        Result = "Fail",
                                        Party_Name = "",
                                        Party_ID = ""
                                    };
                                }

                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            _logger.LogError(ex, "Error in SelectDealerName: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in SelectDealerName: {Message}", ex.Message);
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: Initial Set Up vinay n 13/11/24:::
        /// <summary>
        /// To get initial set up
        /// </summary>
        /// <returns>...</returns>
        public async Task<IActionResult> InitialSetup(InitialSetupList Obj, string connString, int LogException)
        {
            JsonResult jr = null;
            JsonResult jrSerReq = null;
            JsonResult jrPartQtn = null;
            JsonResult jrSrvQtn = null;
            JsonResult jrParty = null;
            JsonResult jrProduct = null;
            JsonResult jrJobCard = null;
            JsonResult jrParts = null;
            var result = default(dynamic);
            string query1 = @"SELECT TOP 1 Object_ID 
                     FROM GNM_Object 
                     WHERE UPPER(Object_Description) = @Description";
            string query2 = @"SELECT TOP 1 Object_ID 
                     FROM GNM_Object 
                     WHERE Object_Description = @Description";
            string query3 = @"SELECT TOP 1 Object_ID 
                     FROM GNM_Object 
                     WHERE UPPER(Object_Name) = @Description";

            try
            {
                int objectid = Convert.ToInt32(Obj.ObjectId);
                objectid = objectid == 0 ? await GetObjectIDAsync("HelpDeskUserLandingPage".ToUpper(), connString, query3, LogException) : objectid;
                jr = await _utilityServiceClient.GetInitialSetupAsync(objectid, Obj.User_ID, connString, LogException);
                int ObjFrSR = await GetObjectIDAsync("HelpDeskUserLandingPage".ToUpper(), connString, query3, LogException);
                jrSerReq = await _utilityServiceClient.GetInitialSetupAsync(ObjFrSR, Obj.User_ID, connString, LogException);
                int ObjFrSrvQT = await GetObjectIDAsync("PST_CustomerQuotation".ToUpper(), connString, query3, LogException);
                int ObjFrPrtQT = await GetObjectIDAsync("PST_CustomerQuotation".ToUpper(), connString, query3, LogException);
                int ObjFrJobC = await GetObjectIDAsync("SRT_JobCard".ToUpper(), connString, query3, LogException);

                ObjFrSrvQT = await GetObjectIDAsync("SERVICE QUOTATION".ToUpper(), connString, query1, LogException);
                int ObjectID = await GetObjectIDAsync("SERVICE QUOTATION", connString, query2, LogException);



                jrPartQtn = await _utilityServiceClient.GetInitialSetupAsync(ObjFrPrtQT, Obj.User_ID, connString, LogException);
                jrSrvQtn = await _utilityServiceClient.GetInitialSetupAsync(ObjFrSrvQT, Obj.User_ID, connString, LogException);
                jrJobCard = await _utilityServiceClient.GetInitialSetupAsync(ObjFrJobC, Obj.User_ID, connString, LogException);
                int ObjFrParty = await GetObjectIDAsync("CorePartyMaster".ToUpper(), connString, query3, LogException);
                jrParty = await _utilityServiceClient.GetInitialSetupAsync(ObjFrParty, Obj.User_ID, connString, LogException);

                int ObjFrProduct = await GetObjectIDAsync("CoreProductMaster".ToUpper(), connString, query3, LogException);
                jrProduct = await _utilityServiceClient.GetInitialSetupAsync(ObjFrProduct, Obj.User_ID, connString, LogException);

                int ObjFrParts = await GetObjectIDAsync("CorePartsMaster".ToUpper(), connString, query3, LogException);
                jrParts = await _utilityServiceClient.GetInitialSetupAsync(ObjFrParts, Obj.User_ID, connString, LogException);

                var IsAddForQuotation = await CheckAddPermissionsAsync("PST_CustomerQuotation", "Quotation", Obj.HelpDesk, Obj.Company_ID, LogException, Obj.User_ID, connString);
                var IsAddForJobCard = await CheckAddPermissionsAsync("SRT_JobCard", "Service Job Card", Obj.HelpDesk, Obj.Company_ID, LogException, Obj.User_ID, connString);
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                bool IsPartyProductAssociation = false;
                bool IsStandAloneService = false;
                bool CHANGESERIALNUMTOVCNUM = false;
                bool IsCallOwnerMandatory = false;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"SELECT Param_Name, Param_value 
                     FROM GNM_CompParam 
                     WHERE Company_ID = @Company_ID 
                     AND Param_Name IN ('PARTYPRODUCTASSOCIATION', 'ISSTANDALONESERVICE', 'CHANGESERIALNUMTOVCNUM')
                     UNION ALL
                     SELECT Param_Name, Param_value 
                     FROM GNM_CompParam 
                     WHERE Param_Name = 'ISCALLOWNERMANDATORY'";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    string paramName = reader["Param_Name"].ToString().ToUpper();
                                    string paramValue = reader["Param_value"].ToString().ToUpper();
                                    switch (paramName)
                                    {
                                        case "PARTYPRODUCTASSOCIATION":
                                            IsPartyProductAssociation = Convert.ToBoolean(paramValue);
                                            break;
                                        case "ISSTANDALONESERVICE":
                                            IsStandAloneService = Convert.ToBoolean(paramValue);
                                            break;
                                        case "CHANGESERIALNUMTOVCNUM":
                                            CHANGESERIALNUMTOVCNUM = paramValue == "TRUE";
                                            break;
                                        case "ISCALLOWNERMANDATORY":
                                            IsCallOwnerMandatory = paramValue == "TRUE";
                                            break;
                                    }
                                }

                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            _logger.LogError(ex, "Error in InitialSetup database operation: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                bool needtochangePassword = Convert.ToBoolean(Obj.NeedToChangepassword);
                result = new
                {
                    obj1 = jr,
                    obj2 = jrSerReq,
                    IsPartyProductAssociation = IsPartyProductAssociation,
                    obj3 = jrPartQtn,
                    obj4 = jrSrvQtn,
                    obj5 = jrParty,
                    obj6 = jrProduct,
                    obj7 = jrJobCard,
                    ServiceQuotationObjectID = ObjFrSrvQT,
                    PartsQuotationObjectID = ObjFrPrtQT,
                    IsAddForQuotation,
                    IsAddForJobCard,
                    IsStandAloneService,
                    CHANGESERIALNUMTOVCNUM,
                    IsCallOwnerMandatory,
                    needtochangePassword,
                    jrParts
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in InitialSetup: {Message}", ex.Message);
                }
            }

            return new JsonResult(result);
        }

        #endregion

        #region ::: To Check Permissions:::
        /// <summary>
        /// To check add permissions for a user
        /// </summary>
        /// <returns>...</returns>
        public async Task<bool> CheckAddPermissionsAsync(string name, string WFName, string HelpDesk, int Company_ID, int LogException, int User_ID, string connString)
        {
            bool IsADD = false;
            int ObjectID = 0;
            int WorkFlowID = 0;
            int CompanyID = 0;
            string query1 = @"SELECT TOP 1 Object_ID
                     FROM GNM_Object
                     WHERE UPPER(Object_Description) = @Description";
            try
            {
                ObjectID = await GetObjectIDAsync(name.ToUpper(), connString, query1, LogException);

                WorkFlowID = await _utilityServiceClient.GetWorkFlowIDAsync(WFName, HelpDesk, connString, LogException);
                CompanyID = Convert.ToInt32(Company_ID);

                IsADD = await _utilityServiceClient.CheckIsAddRecordsAsync(ObjectID, WorkFlowID, CompanyID, User_ID, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in CheckAddPermissions: {Message}", ex.Message);
                }
            }
            return IsADD;
        }
        #endregion

        #region :::To Get ObjectID :::
        /// <summary>
        /// To Get ObjectID
        /// </summary>
        /// <returns>...</returns>
        public async Task<int> GetObjectIDAsync(string name, string connString, string query, int LogException)
        {
            int ObjFrSrvQT = 0;
            using (SqlConnection conn = new SqlConnection(connString))
            {

                SqlCommand cmd = null;
                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@Description", name);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                ObjFrSrvQT = Convert.ToInt32(reader["Object_ID"]);
                            }
                            return ObjFrSrvQT;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        _logger.LogError(ex, "Error in GetObjectID: {Message}", ex.Message);
                    }
                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            return ObjFrSrvQT;
        }
        #endregion

        #region ::: Select Work Flow summary  vinay n 13/11/24:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        public async Task<IActionResult> SelWorkFlowSummary(GetWorkFlowSummaryList Obj, string connString, int LogException)
        {



            dynamic dummy = null;
            var x = dummy;
            try
            {

                int userID = Obj.User_ID;
                Obj.WorkFlow_ID = 1;
                List<WorkFlowSummary> summary = await GetWorkFlowSummary(Obj, connString, LogException);

                // Create the result with localized names using HTTP call to PBC.UtilityService
                var resultList = new List<object>();
                foreach (var a in summary)
                {
                    var localizedName = await _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), a.StatusName);
                    resultList.Add(new
                    {
                        ID = a.StatusID,
                        Name = localizedName,
                        Count = a.Count,
                        MaxValue = a.MaxValue,
                        Mode = a.Mode,
                    });
                }
                x = resultList;

                MaxStatusValue = (summary.Count == 0) ? 0 : summary.ElementAt(0).MaxValue;
                return new JsonResult(x);
            }
            catch (Exception ex)
            {
                return new JsonResult(x);
            }
        }
        #endregion
        #region GetWorkFlowSummary  vinay n 13/11/24
        public async Task<List<WorkFlowSummary>> GetWorkFlowSummary(GetWorkFlowSummaryList Obj, string connString, int LogException)
        {
            List<WorkFlowSummary> WFSummary = new List<WorkFlowSummary>();
            List<WF_WFStepStatus> stepStatuses = new List<WF_WFStepStatus>();
            IEnumerable<WF_WFStepStatus> StepStutus = null;
            List<HD_ServiceLevelAgreement> Agreement = new List<HD_ServiceLevelAgreement>();
            int companyID = Obj.Company_ID;
            int branchID = Obj.Branch_ID;
            int workFlowID = Obj.WorkFlow_ID;
            int UserID = Obj.User_ID;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "SELECT * FROM GNM_WFStepStatus";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    WF_WFStepStatus status = new WF_WFStepStatus
                                    {
                                        WFStepStatus_ID = reader.GetInt32(0),
                                        WFStepStatus_Nm = reader.GetString(1),
                                        StepStatusCode = reader.GetString(2)
                                    };

                                    stepStatuses.Add(status);
                                }
                                StepStutus = stepStatuses.AsEnumerable();
                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            _logger.LogError(ex, "Error in GetWorkFlowSummary: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT *
                        FROM HD_ServiceLevelAgreement
                        WHERE Company_ID = @Company_ID 
                            AND [ServiceLevelAgreementHours_IsActive] = 1";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    HD_ServiceLevelAgreement agreement = new HD_ServiceLevelAgreement
                                    {
                                        ServiceLevelAgreement_ID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0),
                                        CallComplexity_ID = reader.IsDBNull(1) ? 0 : reader.GetInt32(1),
                                        CallPriority_ID = reader.IsDBNull(2) ? 0 : reader.GetInt32(2),
                                        ServiceLevelAgreement_Hours = reader.IsDBNull(3) ? 0m : reader.GetDecimal(3), // 0m for decimal default
                                        Company_ID = reader.IsDBNull(4) ? 0 : reader.GetInt32(4),
                                        ServiceLevelAgreementHours_IsActive = reader.IsDBNull(5) ? false : reader.GetBoolean(5),
                                        Party_ID = reader.IsDBNull(6) ? 0 : reader.GetInt32(6)
                                    };


                                    Agreement.Add(agreement);
                                }

                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            _logger.LogError(ex, "Error in GetWorkFlowSummary: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }



                string Query = string.Empty;
                string ServiceRequestQuery = string.Empty;
                Query = "DECLARE @TempWorkFlowSummary AS TABLE(StatusID int not null,StatusName varchar(50) not null,MaxValue int not null,Mode int not null,Count int not null)";
                Query += " DECLARE @TempCaseProgress AS TABLE (TransactionID int not null)";
                Query += " DECLARE @TempServiceRequest AS TABLE (ServiceRequest_ID int not null)";
                Query += " DECLARE @TempClosedStepIDs AS TABLE (WFSteps_ID int not null)";
                Query += " DECLARE @TempWFSteps AS TABLE (Row int not null,WFSteps_ID int not null,WorkFlow_ID int not null,WFStep_Name varchar(50),WFStepType_ID int not null,WFStepStatus_ID int not null,WFStep_IsActive bit not null)";
                Query += " SELECT ROW_NUMBER() OVER(ORDER BY WFStepStatus_ID) AS Row,* INTO #TempStepStatus FROM GNM_WFStepStatus";
                bool IsBranchSpecific = false;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT TOP 1 [AllQueue_Filter_IsBranch]
                        FROM GNM_WorkFlow
                        WHERE [WorkFlow_ID] = @WorkFlow_ID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlow_ID", workFlowID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }



                            var result = cmd.ExecuteScalar();
                            if (result != null)
                            {
                                IsBranchSpecific = Convert.ToBoolean(result);
                            }






                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            _logger.LogError(ex, "Error in GetWorkFlowSummary: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                if (IsBranchSpecific)
                {
                    Query += " SELECT ServiceRequest_ID INTO #TempServiceRequest FROM HD_ServiceRequest WHERE Company_ID=" + companyID + " AND Branch_ID=" + branchID;

                    ServiceRequestQuery = "SELECT ServiceRequest_ID,CallDateAndTime,CallPriority_ID,CallComplexity_ID,CallStatus_ID,CallClosureDateAndTime,Party_ID FROM HD_ServiceRequest WHERE Company_ID= " + companyID + " AND Branch_ID=" + branchID;
                }
                else
                {
                    Query += " SELECT ServiceRequest_ID INTO #TempServiceRequest FROM HD_ServiceRequest WHERE Company_ID=" + companyID;

                    ServiceRequestQuery = "SELECT ServiceRequest_ID,CallDateAndTime,CallPriority_ID,CallComplexity_ID,CallStatus_ID,CallClosureDateAndTime,Party_ID FROM HD_ServiceRequest WHERE Company_ID= " + companyID;
                }

                Query += " SELECT WFSteps_ID INTO #TempClosedStepIDs FROM GNM_WFSteps WHERE WFStepType_ID in(SELECT WFStepType_ID FROM GNM_WFStepType WHERE WFStepType_Nm= 'END')";

                Query += " DECLARE @StepStatusCount int; SET @StepStatusCount = (SELECT COUNT(*) FROM #TempStepStatus);";
                Query += " DECLARE @i int; SET @i=1;";
                Query += " WHILE (@i <= @StepStatusCount )";
                Query += " BEGIN";
                Query += " DECLARE @WFStepStatus_ID int;DECLARE @WFStepStatus_Nm varchar(50);DECLARE @MaxValue int;DECLARE @Mode int;DECLARE @Count int";
                Query += " SET @WFStepStatus_ID = (SELECT WFStepStatus_ID FROM #TempStepStatus WHERE Row=@i)";
                Query += " SET @WFStepStatus_Nm = (SELECT WFStepStatus_Nm FROM #TempStepStatus WHERE Row=@i)";
                Query += " SET @MaxValue = (SELECT MAX(WFStepStatus_ID) FROM #TempStepStatus)";
                Query += " SET @Mode = 4;SET @Count = 0";
                Query += " DELETE FROM @TempWFSteps";
                Query += " INSERT INTO @TempWFSteps SELECT  ROW_NUMBER() OVER(ORDER BY WFSteps_ID) AS Row,   WFSteps_ID,   WorkFlow_ID,    WFStep_Name,    WFStepType_ID,    WFStepStatus_ID,    WFStep_IsActive FROM GNM_WFSteps WHERE WFStepStatus_ID = @WFStepStatus_ID AND WorkFlow_ID=" + workFlowID;
                Query += " DECLARE @StepsCount int;SET @StepsCount = (SELECT COUNT(*) FROM @TempWFSteps)";
                Query += " DECLARE @j int; set @j=1;";
                Query += " WHILE(@j <= @StepsCount)";
                Query += " BEGIN";
                Query += " DECLARE @WFSteps_ID int;SET @WFSteps_ID = (SELECT WFSteps_ID FROM @TempWFSteps WHERE Row=@j)";
                Query += " IF ((SELECT COUNT(*) FROM #TempClosedStepIDs WHERE WFSteps_ID = @WFSteps_ID) > 0)";
                Query += " BEGIN";
                Query += " DELETE FROM @TempCaseProgress";
                Query += " insert INTO	@TempCaseProgress SELECT CP.Transaction_ID FROM GNM_WFCase_Progress CP WHERE CP.WorkFlow_ID=" + workFlowID + " AND ((SELECT COUNT(*) FROM #TempClosedStepIDs WHERE WFSteps_ID = CP.WFNextStep_ID) > 0)";
                Query += " DELETE FROM #TempClosedStepIDs";
                Query += " END";
                Query += " ELSE";
                Query += " BEGIN";
                Query += " DELETE FROM @TempCaseProgress";
                Query += " INSERT INTO	@TempCaseProgress SELECT CP.Transaction_ID FROM GNM_WFCase_Progress CP WHERE CP.WorkFlow_ID=" + workFlowID + " AND CP.WFSteps_ID = @WFSteps_ID AND CP.Action_Chosen is NULL";
                Query += " END";
                Query += " IF (" + workFlowID + "=" + 1 + ")";
                Query += " BEGIN";
                Query += " DECLARE @Count1 int;SET @Count1 = (SELECT COUNT(*) FROM @TempCaseProgress JOIN #TempServiceRequest ON TransactionID = ServiceRequest_ID)";
                Query += " SET @Count = @Count + @Count1";
                Query += " END";
                Query += " SET @j= @j + 1;";
                Query += " END";
                Query += " IF(@Count >0 )";
                Query += " BEGIN";
                Query += " INSERT INTO @TempWorkFlowSummary (StatusID, StatusName, MaxValue, Mode, Count)\r\nVALUES (@WFStepStatus_ID, @WFStepStatus_Nm, @MaxValue, @Mode, @Count);";
                Query += " END";
                Query += " SET @i= @i + 1;";
                Query += " END";

                //My Queue----
                Query += " DECLARE @MyQueueCount int; SET @MyQueueCount=0;";
                Query += " SET @MyQueueCount = (SELECT COUNT(WFCaseProgress_ID) FROM GNM_WFCase_Progress JOIN #TempServiceRequest ON Transaction_ID = ServiceRequest_ID WHERE Action_Chosen is null AND WorkFlow_ID=" + workFlowID + " AND Addresse_Flag =1 AND Addresse_ID = " + UserID + ")";
                Query += " IF(@MyQueueCount != 0)";
                Query += " BEGIN";
                Query += " INSERT INTO @TempWorkFlowSummary VALUES ((select MAX(WFStepStatus_ID) from #TempStepStatus)+1 ,'MyQueue',(select MAX(WFStepStatus_ID) from #TempStepStatus),1,@MyQueueCount);";
                Query += " END";
                //-----------

                Query += " SELECT * FROM @TempWorkFlowSummary";
                Query += " DROP TABLE #TempServiceRequest,#TempStepStatus,#TempClosedStepIDs";

                List<WorkFlowSummary> Result = GetValueFromDB<List<WorkFlowSummary>>(Query, null, connString);


                WFSummary.AddRange(Result);
                List<ServiceRequest> SRequset = new List<ServiceRequest>();
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(ServiceRequestQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {

                                    ServiceRequest serviceRequest = new ServiceRequest
                                    {
                                        ServiceRequest_ID = reader.GetInt32(reader.GetOrdinal("ServiceRequest_ID")),
                                        CallDateAndTime = reader.GetDateTime(reader.GetOrdinal("CallDateAndTime")),
                                        CallPriority_ID = reader.GetInt32(reader.GetOrdinal("CallPriority_ID")),
                                        CallComplexity_ID = reader.GetInt32(reader.GetOrdinal("CallComplexity_ID")),

                                        CallClosureDateAndTime = reader.IsDBNull(reader.GetOrdinal("CallClosureDateAndTime")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("CallClosureDateAndTime")),
                                        Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID"))
                                    };
                                    SRequset.Add(serviceRequest);
                                }

                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            _logger.LogError(ex, "Error in GetWorkFlowSummary: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }


                var groupQueueResult = await _utilityServiceClient.GetGroupQueueAsync(companyID, connString, workFlowID, UserID);
                List<dynamic> RowIndGroup = groupQueueResult;
                //Added By Puneeth M on 24-Apr-2014 for Call Number AHD-2014042401
                if (workFlowID == 1)
                {
                    RowIndGroup = (from dynamic a in RowIndGroup
                                   join b in SRequset on (int)a.TransactionID equals b.ServiceRequest_ID
                                   select a).ToList();
                }
                //
                if (RowIndGroup.Count > 0)
                {
                    WorkFlowSummary SummaryGroupQueue = new WorkFlowSummary();
                    SummaryGroupQueue.StatusID = StepStutus.Max(a => a.WFStepStatus_ID) + 2;
                    SummaryGroupQueue.StatusName = "GroupQueue";
                    SummaryGroupQueue.Count = RowIndGroup.Count;
                    SummaryGroupQueue.Mode = 2;
                    SummaryGroupQueue.MaxValue = StepStutus.Max(a => a.WFStepStatus_ID);
                    WFSummary.Add(SummaryGroupQueue);
                }

                var allQueueResult = await _utilityServiceClient.GetAllQueueAsync(companyID, workFlowID, UserID, 0, branchID, connString, LogException);
                List<dynamic> RowIndAll = allQueueResult;
                //Added By Puneeth M on 24-Apr-2014 for Call Number AHD-2014042401
                if (workFlowID == 1)
                {
                    RowIndAll = (from dynamic a in RowIndAll
                                 join b in SRequset on (int)a.TransactionID equals b.ServiceRequest_ID
                                 select a).ToList();
                }
                //
                if (RowIndAll.Count > 0)
                {
                    WorkFlowSummary SummaryAllQueue = new WorkFlowSummary();
                    SummaryAllQueue.StatusID = StepStutus.Max(a => a.WFStepStatus_ID) + 3;
                    SummaryAllQueue.StatusName = "AllQueue";
                    SummaryAllQueue.Count = RowIndAll.Count;
                    SummaryAllQueue.Mode = 3;
                    SummaryAllQueue.MaxValue = StepStutus.Max(a => a.WFStepStatus_ID);
                    WFSummary.Add(SummaryAllQueue);
                }
            }
            catch (Exception ex)
            {

            }
            return WFSummary;
        }
        #endregion

        #region validateCalldateandPCD vinay n 13/11/24
        public IActionResult validateCalldateandPCD(validateCalldateandPCDList Obj)
        {
            int count = 0;

            if (Obj.PCD < Obj.Calldate)
            {
                count = 1;
            }
            return new JsonResult(count);
        }
        #endregion

        #region ::: CheckBayWorkshopAvailability vinay n 13/11/24:::
        /// <summary>
        /// To check bay and workshop availability
        /// </summary>
        public async Task<IActionResult> CheckBayWorkshopAvailability(CheckBayWorkshopAvailabilityList Obj, string connString)
        {
            int AvailableStatus = 0;
            int Branch_ID = Convert.ToInt32(Obj.Branch);
            SqlConnection SQLConn;
            SqlCommand SQLCmd;
            SqlDataReader SQLDr;

            try
            {
                using (SQLConn = new SqlConnection(connString))
                {
                    if (SQLConn.State == ConnectionState.Closed || SQLConn.State == ConnectionState.Broken)
                    {
                        SQLConn.Open();
                        string Query = "DECLARE @AvailableStatus int exec @AvailableStatus= Up_CheckBayWorkshopAvailability @Branch_ID,@ExpectedArrivalDate,@ExpectedDepartureDate,@IsWIPBay,@BookingMinutes,@ServiceRequest_ID,@Quotation_ID SELECT @AvailableStatus";
                        SQLCmd = new SqlCommand(Query, SQLConn);
                        SQLCmd.CommandType = CommandType.Text;
                        SQLCmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                        SQLCmd.Parameters.AddWithValue("@ExpectedArrivalDate", Obj.ExpectedArrivalDate);
                        SQLCmd.Parameters.AddWithValue("@ExpectedDepartureDate", Obj.ExpectedDepartureDate);
                        SQLCmd.Parameters.AddWithValue("@IsWIPBay", Obj.IsWIPBay);
                        SQLCmd.Parameters.AddWithValue("@BookingMinutes", Obj.BookingMinutes);
                        SQLCmd.Parameters.AddWithValue("@ServiceRequest_ID", Obj.ServiceRequest_ID);
                        SQLCmd.Parameters.AddWithValue("@Quotation_ID", Obj.Quotation_ID);
                        AvailableStatus = Convert.ToInt32(SQLCmd.ExecuteScalar());
                    }
                    SQLConn.Close();
                }
            }
            catch (Exception ex)
            {
                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                _logger.LogError(ex, "Error in GetWorkFlowSummary: {Message}", ex.Message);
            }
            return new JsonResult(AvailableStatus);
        }
        #endregion

        #region ::: CheckForWorkshopBlockOverlap vinay n 13/11/24:::
        /// <summary>
        /// To check workshop block overlapping
        /// </summary>
        public async Task<IActionResult> CheckForWorkshopBlockOverlapAsync(CheckForWorkshopBlockOverlapList Obj, string Connstring, int LogException)
        {
            int AvailableStatus = 0;
            int Branch_ID = Convert.ToInt32(Obj.Branch);

            try
            {
                using (var SQLConn = new SqlConnection(Connstring))
                {
                    if (SQLConn.State == ConnectionState.Closed || SQLConn.State == ConnectionState.Broken)
                    {
                        await SQLConn.OpenAsync();
                        string Query = "DECLARE @AvailableStatus int exec @AvailableStatus= Up_CheckForWorkshopBlockOverlap @Branch_ID,@ExpectedArrivalDate,@ExpectedDepartureDate,@IsFromQuote,@ServiceRequest_ID,@Quotation_ID,@VIN SELECT @AvailableStatus";
                        using (var SQLCmd = new SqlCommand(Query, SQLConn))
                        {
                            SQLCmd.CommandType = CommandType.Text;
                            SQLCmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                            SQLCmd.Parameters.AddWithValue("@ExpectedArrivalDate", Obj.ExpectedArrivalDate);
                            SQLCmd.Parameters.AddWithValue("@ExpectedDepartureDate", Obj.ExpectedDepartureDate);
                            SQLCmd.Parameters.AddWithValue("@IsFromQuote", Obj.IsFromQuote);
                            SQLCmd.Parameters.AddWithValue("@ServiceRequest_ID", Obj.ServiceRequest_ID);
                            SQLCmd.Parameters.AddWithValue("@Quotation_ID", Obj.Quotation_ID);
                            SQLCmd.Parameters.AddWithValue("@VIN", Obj.VIN);
                            var result = await SQLCmd.ExecuteScalarAsync();
                            AvailableStatus = Convert.ToInt32(result);
                        }
                        await SQLConn.CloseAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in CheckForWorkshopBlockOverlap: {Message}", ex.Message);
                }
            }
            return new JsonResult(AvailableStatus);
        }
        #endregion

        #region SelectFieldSearchParty vinay n 13/11/24
        public async Task<IActionResult> SelectFieldSearchPartyAsync(SelectFieldSearchParty2List Obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {

            string values = (Obj.value)?.Trim() ?? string.Empty;
            string value = await _utilityServiceClient.DecryptStringAsync(values);
            value = value.Trim();

            object[] paramtrs = { value };

            IEnumerable<FieldSearch> flSrch = null;
            int Count = 0;
            int Total = 0;
            int Company_ID = Convert.ToInt32(Obj.Company_ID);
            int Branch_ID = Convert.ToInt32(Obj.Branch);
            string WhereCondition = string.Empty;
            string CountQuery = string.Empty;
            string Query = "";
            int LangID = Convert.ToInt32(Obj.LanguageID);
            string GenLangCode = Obj.GeneralLanguageCode.ToString();
            string UserLangCode = Obj.UserLanguageCode.ToString();
            if (_search)
            {
                string decodedValue = Uri.UnescapeDataString(filters);
                Filters filtersObj = JObject.Parse(await _utilityServiceClient.DecryptStringAsync(decodedValue)).ToObject<Filters>();
                if (filtersObj.rules.Count > 0)
                {
                    for (int i = 0; i < filtersObj.rules.Count(); i++)
                    {
                        if (filtersObj.rules.ElementAt(i).field == "Name")
                        {
                            value = await _utilityServiceClient.DecryptStringAsync(filtersObj.rules.ElementAt(i).data);
                        }
                        else if (filtersObj.rules.ElementAt(i).field == "Email")
                        {
                            filtersObj.rules.ElementAt(i).field = "Party_Email";
                        }
                        else if (filtersObj.rules.ElementAt(i).field == "MobileNumber")
                        {
                            filtersObj.rules.ElementAt(i).field = "Party_Mobile";
                        }
                        else if (filtersObj.rules.ElementAt(i).field == "Location")
                        {
                            filtersObj.rules.ElementAt(i).field = "Party_Location";
                        }
                        if (filtersObj.rules.ElementAt(i).field != "Name")
                        {
                            WhereCondition = WhereCondition + " AND " + filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%'";
                        }
                    }
                }
                else { value = ""; }
            }
            if (sidx == "Name")
            {
                if (GenLangCode == UserLangCode)
                { sidx = "Party_Name"; }
                else { sidx = "c.Party_Name"; }
            }
            if (sidx == "Email") { sidx = "Party_Email"; }
            if (sidx == "MobileNumber") { sidx = "Party_Mobile"; }
            if (sidx == "Location") { sidx = "Party_Location"; }


            if (GenLangCode == UserLangCode)
            {

                if (value != "")
                {
                    WhereCondition += " AND Party_Name like'%'+@value+'%'";
                }
                Query = ";WITH NewT AS(select a.Party_ID,Party_Name as Name,Party_Location as Location,Party_Mobile as MobileNumber,Party_Email as Email,Party_Code,PartyAddress_Address,ROW_NUMBER() OVER(ORDER BY " + sidx + " " + sord + ") as row_number from GNM_Party a left outer join GNM_PartyAddress b on a.Party_ID=b.Party_ID where b.PartyAddress_Active=1 and b.IsDefault=1 and Party_IsActive=1 and (PartyType=1 or PartyType=2) " + WhereCondition + ") SELECT * FROM NewT WHERE row_number BETWEEN " + (((page - 1) * (rows)) + 1) + " AND " + (page * rows);
                CountQuery = "select count(a.Party_ID) from GNM_Party a left outer join GNM_PartyAddress b on a.Party_ID=b.Party_ID where b.PartyAddress_Active=1 and b.IsDefault=1 and Party_IsActive=1 and (PartyType=1 or PartyType=2) " + WhereCondition;
            }
            else
            {

                if (value != "")
                {
                    WhereCondition += " AND c.Party_Name like'%'+@value+'%'";
                }
                Query = ";WITH NewT AS(select a.Party_ID,c.Party_Name as Name,c.Party_Location as Location,Party_Mobile as MobileNumber,Party_Email as Email,a.Party_Code,d.PartyAddressLocale_Address as PartyAddress_Address,ROW_NUMBER() OVER(ORDER BY " + sidx + " " + sord + ") as row_number from GNM_Party a left outer join GNM_PartyAddress b on a.Party_ID=b.Party_ID join GNM_PartyLocale c on a.Party_ID=c.Party_ID join GNM_PartyAddressLocale d on b.PartyAddress_ID=d.PartyAddress_ID where b.PartyAddress_Active=1 and b.IsDefault=1 and Party_IsActive=1 and (PartyType=1 or PartyType=2) " + WhereCondition + ") SELECT * FROM NewT WHERE row_number BETWEEN " + (((page - 1) * (rows)) + 1) + " AND " + (page * rows);
                CountQuery = "select count(a.Party_ID) from GNM_Party a left outer join GNM_PartyAddress b on a.Party_ID=b.Party_ID join GNM_PartyLocale c on a.Party_ID=c.Party_ID join GNM_PartyAddressLocale d on b.PartyAddress_ID=d.PartyAddress_ID where b.PartyAddress_Active=1 and b.IsDefault=1 and Party_IsActive=1 and (PartyType=1 or PartyType=2) " + WhereCondition;
            }
            List<FieldSearch> fsrhList = new List<FieldSearch>();
            List<int> countRes = new List<int>();
            IEnumerable<int> rcount = null;
            using (SqlConnection conn = new SqlConnection(connString))
            {


                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(Query, conn))
                    {
                        cmd.CommandType = CommandType.Text;

                        cmd.Parameters.AddWithValue("@value", value);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                var fieldSearch = new FieldSearch
                                {
                                    Party_ID = reader["Party_ID"] as int?,
                                    Name = reader["Name"] as string,
                                    Location = reader["Location"] as string,
                                    MobileNumber = reader["MobileNumber"] as string,
                                    Email = reader["Email"] as string,
                                    Party_Code = reader["Party_Code"] as string,
                                    PartyAddress_Address = reader["PartyAddress_Address"] as string

                                };
                                fsrhList.Add(fieldSearch);

                            }
                            flSrch = fsrhList.AsEnumerable();
                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        _logger.LogError(ex, "Error in SelectFieldSearchParty data retrieval: {Message}", ex.Message);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }

            using (SqlConnection conn = new SqlConnection(connString))
            {


                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(CountQuery, conn))
                    {
                        cmd.CommandType = CommandType.Text;

                        cmd.Parameters.AddWithValue("@value", value);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        int resultCount = (int)cmd.ExecuteScalar();
                        countRes.Add(resultCount);

                        rcount = countRes.AsEnumerable();


                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        _logger.LogError(ex, "Error in SelectFieldSearchParty count query: {Message}", ex.Message);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }


            Count = rcount.ElementAt(0);
            Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

            if (Count < (rows * page) && Count != 0)
            {
                page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
            }

            string Select = await _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "select");
            var jsonData = new
            {
                total = Total,
                page = page,
                records = Count,
                rows = (from q in flSrch.AsEnumerable()
                        select new
                        {
                            ID = q.ID,
                            Name = q.Name,
                            Location = q.Location,
                            MobileNumber = q.MobileNumber,
                            Email = q.Email,
                            //Select = "<img id='" + q.ID + "' class='FieldSrch' src='" + Common.appPath + "/images/accept.png'></img>",
                            Select = "<a title=" + Select + " href='#' style='font-size: 13px;' id='" + q.ID + "' class='FieldSrch' style='cursor:pointer'><i class='fa fa-check'></i></a>",
                            Party_Code = q.Party_Code,
                            PartyAddress_Address = q.PartyAddress_Address

                        }
                ).ToList(),
            };


            return new JsonResult(jsonData);

        }
        #endregion

        #region ::: GetSLAExceeded vinay n 14/11/24:::
        /// <summary>
        /// GetSLAExceeded
        /// </summary>
        /// <returns>...</returns>
        public async Task<string> GetSLAExceeded(int companyID, int workFlowID, int userID, int branchID, string connString, int LogException, string HelpDesk)
        {
            int CloseStepID = 0;
            int roleID = 0;
            List<Indicator> RowInd = new List<Indicator>();
            List<HD_ServiceRequest> res = new List<HD_ServiceRequest>();
            IEnumerable<HD_ServiceRequest> SRequset = null;
            IEnumerable<HD_ServiceLevelAgreement> Agreement = null;
            List<HD_ServiceLevelAgreement> agrRes = new List<HD_ServiceLevelAgreement>();
            int ClosedStatusID = await _utilityServiceClient.GetEndStepStatusIDAsync(await _utilityServiceClient.GetWorkFlowIDAsync("Case Registration", HelpDesk, connString, LogException), connString, LogException);

            using (SqlConnection conn = new SqlConnection(connString))
            {


                SqlCommand cmd = null;

                try
                {
                    string query = @"
                        SELECT *
                        FROM HD_ServiceRequest
                        WHERE Company_ID = @CompanyID
                        AND Branch_ID = @BranchID
                        AND CallStatus_ID != @ClosedStatusID
                        AND CallClosureDateAndTime IS NULL
                    ";
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;

                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@BranchID", branchID);
                        cmd.Parameters.AddWithValue("@ClosedStatusID", ClosedStatusID);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                HD_ServiceRequest serviceRequest = new HD_ServiceRequest
                                {
                                    ServiceRequest_ID = reader.GetInt32(reader.GetOrdinal("ServiceRequest_ID")),
                                    ServiceRequestNumber = reader.IsDBNull(reader.GetOrdinal("ServiceRequestNumber")) ? null : reader.GetString(reader.GetOrdinal("ServiceRequestNumber")),
                                    ServiceRequestDate = reader.GetDateTime(reader.GetOrdinal("ServiceRequestDate")),
                                    Quotation_ID = reader.IsDBNull(reader.GetOrdinal("Quotation_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Quotation_ID")),
                                    QuotationNumber = reader.IsDBNull(reader.GetOrdinal("QuotationNumber")) ? null : reader.GetString(reader.GetOrdinal("QuotationNumber")),
                                    JobCard_ID = reader.IsDBNull(reader.GetOrdinal("JobCard_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("JobCard_ID")),
                                    JobCardNumber = reader.IsDBNull(reader.GetOrdinal("JobCardNumber")) ? null : reader.GetString(reader.GetOrdinal("JobCardNumber")),
                                    CallStatus_ID = reader.GetInt32(reader.GetOrdinal("CallStatus_ID")),
                                    ParentIssue_ID = reader.IsDBNull(reader.GetOrdinal("ParentIssue_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ParentIssue_ID")),
                                    Product_Unique_Number = reader.IsDBNull(reader.GetOrdinal("Product_Unique_Number")) ? null : reader.GetString(reader.GetOrdinal("Product_Unique_Number")),
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                    PartyContactPerson_ID = reader.GetInt32(reader.GetOrdinal("PartyContactPerson_ID")),
                                    Model_ID = reader.IsDBNull(reader.GetOrdinal("Model_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                    Brand_ID = reader.IsDBNull(reader.GetOrdinal("Brand_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                    ProductType_ID = reader.IsDBNull(reader.GetOrdinal("ProductType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                    SerialNumber = reader.IsDBNull(reader.GetOrdinal("SerialNumber")) ? null : reader.GetString(reader.GetOrdinal("SerialNumber")),
                                    ProductReading = reader.IsDBNull(reader.GetOrdinal("ProductReading")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ProductReading")),
                                    IsUnderWarranty = reader.GetBoolean(reader.GetOrdinal("IsUnderWarranty")),
                                    CallMode_ID = reader.GetInt32(reader.GetOrdinal("CallMode_ID")),
                                    CallPriority_ID = reader.IsDBNull(reader.GetOrdinal("CallPriority_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("CallPriority_ID")),
                                    CallComplexity_ID = reader.GetInt32(reader.GetOrdinal("CallComplexity_ID")),
                                    CallDateAndTime = reader.GetDateTime(reader.GetOrdinal("CallDateAndTime")),
                                    PromisedCompletionDate = reader.IsDBNull(reader.GetOrdinal("PromisedCompletionDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("PromisedCompletionDate")),
                                    Region_ID = reader.IsDBNull(reader.GetOrdinal("Region_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Region_ID")),
                                    CallDescription = reader.IsDBNull(reader.GetOrdinal("CallDescription")) ? null : reader.GetString(reader.GetOrdinal("CallDescription")),
                                    IssueArea_ID = reader.IsDBNull(reader.GetOrdinal("IssueArea_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("IssueArea_ID")),
                                    IssueSubArea_ID = reader.IsDBNull(reader.GetOrdinal("IssueSubArea_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("IssueSubArea_ID")),
                                    FunctionGroup_ID = reader.IsDBNull(reader.GetOrdinal("FunctionGroup_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("FunctionGroup_ID")),
                                    IsUnderBreakDown = reader.GetBoolean(reader.GetOrdinal("IsUnderBreakDown")),
                                    QuestionaryLevel1_ID = reader.IsDBNull(reader.GetOrdinal("QuestionaryLevel1_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("QuestionaryLevel1_ID")),
                                    QuestionaryLevel2_ID = reader.IsDBNull(reader.GetOrdinal("QuestionaryLevel2_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("QuestionaryLevel2_ID")),
                                    QuestionaryLevel3_ID = reader.IsDBNull(reader.GetOrdinal("QuestionaryLevel3_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("QuestionaryLevel3_ID")),
                                    DefectGroup_ID = reader.IsDBNull(reader.GetOrdinal("DefectGroup_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("DefectGroup_ID")),
                                    DefectName_ID = reader.IsDBNull(reader.GetOrdinal("DefectName_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("DefectName_ID")),
                                    RootCause = reader.IsDBNull(reader.GetOrdinal("RootCause")) ? null : reader.GetString(reader.GetOrdinal("RootCause")),
                                    InformationCollected = reader.IsDBNull(reader.GetOrdinal("InformationCollected")) ? null : reader.GetString(reader.GetOrdinal("InformationCollected")),
                                    CallClosureDateAndTime = reader.IsDBNull(reader.GetOrdinal("CallClosureDateAndTime")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("CallClosureDateAndTime")),
                                    ClosureType_ID = reader.IsDBNull(reader.GetOrdinal("ClosureType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ClosureType_ID")),
                                    ClosingDescription = reader.IsDBNull(reader.GetOrdinal("ClosingDescription")) ? null : reader.GetString(reader.GetOrdinal("ClosingDescription")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                    ModifiedBy_ID = reader.GetInt32(reader.GetOrdinal("ModifiedBy_ID")),
                                    Document_no = reader.IsDBNull(reader.GetOrdinal("Document_no")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Document_no")),
                                    CaseType_ID = reader.GetInt32(reader.GetOrdinal("CaseType_ID")),
                                    ActionRemarks = reader.IsDBNull(reader.GetOrdinal("ActionRemarks")) ? null : reader.GetString(reader.GetOrdinal("ActionRemarks")),
                                    Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                    CustomerRating = reader.IsDBNull(reader.GetOrdinal("CustomerRating")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("CustomerRating")),
                                    FinancialYear = reader.IsDBNull(reader.GetOrdinal("FinancialYear")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("FinancialYear")),
                                    IsCallBlocked = reader.GetBoolean(reader.GetOrdinal("IsCallBlocked")),
                                    StockBlocking_ID = reader.IsDBNull(reader.GetOrdinal("StockBlocking_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("StockBlocking_ID")),
                                    EnquiryStage_ID = reader.IsDBNull(reader.GetOrdinal("EnquiryStage_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("EnquiryStage_ID")),
                                    SalesQuotation_ID = reader.IsDBNull(reader.GetOrdinal("SalesQuotation_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SalesQuotation_ID")),
                                    SalesQuotationNumber = reader.IsDBNull(reader.GetOrdinal("SalesQuotationNumber")) ? null : reader.GetString(reader.GetOrdinal("SalesQuotationNumber")),
                                    SalesOrder_ID = reader.IsDBNull(reader.GetOrdinal("SalesOrder_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SalesOrder_ID")),
                                    SalesOrderNumber = reader.IsDBNull(reader.GetOrdinal("SalesOrderNumber")) ? null : reader.GetString(reader.GetOrdinal("SalesOrderNumber")),
                                    SalesOrderDate = reader.IsDBNull(reader.GetOrdinal("SalesOrderDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("SalesOrderDate")),
                                    CallOwner_ID = reader.IsDBNull(reader.GetOrdinal("CallOwner_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("CallOwner_ID")),
                                    Flexi1 = reader.IsDBNull(reader.GetOrdinal("Flexi1")) ? null : reader.GetString(reader.GetOrdinal("Flexi1")),
                                    Flexi2 = reader.IsDBNull(reader.GetOrdinal("Flexi2")) ? null : reader.GetString(reader.GetOrdinal("Flexi2")),
                                    ResolutionTime = reader.IsDBNull(reader.GetOrdinal("ResolutionTime")) ? null : reader.GetString(reader.GetOrdinal("ResolutionTime")),
                                    ResponseTime = reader.IsDBNull(reader.GetOrdinal("ResponseTime")) ? null : reader.GetString(reader.GetOrdinal("ResponseTime")),
                                    AttachmentCount = reader.IsDBNull(reader.GetOrdinal("AttachmentCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("AttachmentCount")),
                                    IsDealer = reader.GetBoolean(reader.GetOrdinal("IsDealer")),
                                    IsDealerList = reader.IsDBNull(reader.GetOrdinal("IsDealerList")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("IsDealerList")),
                                    CustomerFeedbackDate = reader.IsDBNull(reader.GetOrdinal("CustomerFeedbackDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("CustomerFeedbackDate")),
                                    IsNegetiveFeedback = reader.GetBoolean(reader.GetOrdinal("IsNegetiveFeedback")),
                                    ProductRateType = reader.IsDBNull(reader.GetOrdinal("ProductRateType")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("ProductRateType")),
                                    ChildTicket_Sequence_ID = reader.IsDBNull(reader.GetOrdinal("ChildTicket_Sequence_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ChildTicket_Sequence_ID")),
                                    ResponseTime24 = reader.IsDBNull(reader.GetOrdinal("ResponseTime24")) ? null : reader.GetString(reader.GetOrdinal("ResponseTime24")),
                                    ResolutionTime24 = reader.IsDBNull(reader.GetOrdinal("ResolutionTime24")) ? null : reader.GetString(reader.GetOrdinal("ResolutionTime24")),
                                    Current_AssignTo = reader.IsDBNull(reader.GetOrdinal("Current_AssignTo")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Current_AssignTo")),
                                    ContractorID = reader.IsDBNull(reader.GetOrdinal("ContractorID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ContractorID")),
                                    ContractorContactPerson_ID = reader.IsDBNull(reader.GetOrdinal("ContractorContactPerson_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ContractorContactPerson_ID")),
                                    ScheduledType_ID = reader.IsDBNull(reader.GetOrdinal("ScheduledType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ScheduledType_ID")),
                                    ExpectedArrivalDateTime = reader.IsDBNull(reader.GetOrdinal("ExpectedArrivalDateTime")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("ExpectedArrivalDateTime")),
                                    ActualArrivalDateTime = reader.IsDBNull(reader.GetOrdinal("ActualArrivalDateTime")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("ActualArrivalDateTime")),
                                    ExpectedDepartureDateTime = reader.IsDBNull(reader.GetOrdinal("ExpectedDepartureDateTime")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("ExpectedDepartureDateTime")),
                                    ActualDepartureDateTime = reader.IsDBNull(reader.GetOrdinal("ActualDepartureDateTime")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("ActualDepartureDateTime")),
                                    NoofTechs = reader.IsDBNull(reader.GetOrdinal("NoofTechs")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("NoofTechs")),
                                    ShiftHours = reader.IsDBNull(reader.GetOrdinal("ShiftHours")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ShiftHours")),
                                    IsWIPBay = reader.GetBoolean(reader.GetOrdinal("IsWIPBay"))
                                };

                                res.Add(serviceRequest);

                            }
                            SRequset = res.AsEnumerable();
                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        _logger.LogError(ex, "Error in GetSLAExceeded: {Message}", ex.Message);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }
            using (SqlConnection conn = new SqlConnection(connString))
            {


                SqlCommand cmd = null;

                try
                {
                    string query = @"
                        SELECT *
                        FROM HD_ServiceLevelAgreement
                        WHERE [Company_ID] = @CompanyID AND [ServiceLevelAgreementHours_IsActive] = 1";
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;

                        cmd.Parameters.AddWithValue("@CompanyID", companyID);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                HD_ServiceLevelAgreement agreement = new HD_ServiceLevelAgreement
                                {
                                    ServiceLevelAgreement_ID = reader.GetInt32(reader.GetOrdinal("ServiceLevelAgreement_ID")),
                                    CallComplexity_ID = reader.GetInt32(reader.GetOrdinal("CallComplexity_ID")),
                                    CallPriority_ID = reader.GetInt32(reader.GetOrdinal("CallPriority_ID")),
                                    ServiceLevelAgreement_Hours = reader.GetInt32(reader.GetOrdinal("ServiceLevelAgreement_Hours")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    ServiceLevelAgreementHours_IsActive = reader.GetBoolean(reader.GetOrdinal("ServiceLevelAgreementHours_IsActive")),
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID"))
                                };

                                agrRes.Add(agreement);

                            }
                            Agreement = agrRes.AsEnumerable();
                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        _logger.LogError(ex, "Error in GetSLAExceeded: {Message}", ex.Message);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }


            List<WF_WFCase_Progress> transactionList = new List<WF_WFCase_Progress>();
            int CompanyID = Convert.ToInt32(companyID);
            var countData = default(dynamic);
            string CPIDs = string.Empty;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        string _query = @"
                        DECLARE @CloseStepID INT;
                        DECLARE @RoleID INT;

                      
                        SET @CloseStepID = (SELECT TOP 1 WFSteps_ID FROM GNM_WFSteps WHERE WFStepType_ID = 4);
                        SELECT @CloseStepID;
                       
                        SET @RoleID = (SELECT TOP 1 WFRole_ID 
                                        FROM GNM_WFRoleUser AS user
                                        JOIN GNM_WFRole AS role ON user.WFRole_ID = role.WFRole_ID
                                        WHERE role.WorkFlow_ID = @WorkFlowID AND user.UserID = @UserID);

                        SELECT @RoleID;
                        SELECT *
                        FROM GNM_WFCase_Progress
                        WHERE (Action_Chosen = 0 OR WFNextStep_ID = @CloseStepID OR Action_Chosen IS NULL) 
                        AND WorkFlow_ID = @WorkFlowID;
                    ";
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@UserID", userID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    CloseStepID = reader.GetInt32(0);
                                }
                                reader.NextResult();
                                if (reader.Read())
                                {
                                    roleID = reader.GetInt32(0);
                                }
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    WF_WFCase_Progress progress = new WF_WFCase_Progress
                                    {
                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                        Addresse_ID = reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                        Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                        Locked_Ind = reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                    };

                                    transactionList.Add(progress);
                                }
                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetSLAExceeded: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                List<SLA> slaList = new List<SLA>();
                string query = "select CP.WFCaseProgress_ID,SR.CallPriority_ID,SR.CallComplexity_ID,SR.Party_ID as SRPartyID,AG.Party_ID as AGPartyID,AG.ServiceLevelAgreement_Hours,CallDateAndTime"
                                 + " from HD_ServiceRequest as SR"
                                 + " inner join HD_ServiceLevelAgreement as AG on SR.CallPriority_ID=AG.CallPriority_ID"
                                 + " inner join GNM_WFCase_Progress as  CP"
                                 + " on SR.ServiceRequest_ID=CP.Transaction_ID"
                                 + " where (CP.Action_Chosen=0 or CP.WFNextStep_ID=8 or CP.Action_Chosen is null)"
                                 + " and SR.CallComplexity_ID=AG.CallComplexity_ID"
                                 + " and CP.WorkFlow_ID=" + workFlowID + " and CallClosureDateAndTime is null and CallStatus_ID!=" + ClosedStatusID
                                 + " and AG.Company_ID=" + companyID
                                 + " and AG.ServiceLevelAgreementHours_IsActive=1";
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {

                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                SLA sla = new SLA
                                {
                                    WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                    CallPriority_ID = reader.GetInt32(reader.GetOrdinal("CallPriority_ID")),
                                    CallComplexity_ID = reader.GetInt32(reader.GetOrdinal("CallComplexity_ID")),
                                    SRPartyID = reader.GetInt32(reader.GetOrdinal("SRPartyID")),
                                    AGPartyID = reader.IsDBNull(reader.GetOrdinal("AGPartyID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("AGPartyID")),
                                    ServiceLevelAgreement_Hours = reader.GetDecimal(reader.GetOrdinal("ServiceLevelAgreement_Hours")),
                                    CallDateAndTime = reader.GetDateTime(reader.GetOrdinal("CallDateAndTime"))
                                };
                                slaList.Add(sla);
                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetSLAExceeded: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                // Process each SLA item asynchronously to check conditions
                var validSLAItems = new List<dynamic>();
                foreach (var SR in slaList)
                {
                    try
                    {
                        // Check party-specific condition
                        bool partySpecificCheck = await _utilityServiceClient.CheckPartySpecificAsync(SR.SRPartyID, SR.CallComplexity_ID, SR.CallPriority_ID, companyID, connString, LogException);
                        bool partyCondition = partySpecificCheck ? SR.SRPartyID == SR.AGPartyID : SR.AGPartyID == null;

                        // Check working hours condition
                        double workingHours = await _utilityServiceClient.GetWorkingHoursAsync(SR.CallDateAndTime, companyID, connString, LogException);
                        bool hoursCondition = workingHours > Convert.ToDouble(SR.ServiceLevelAgreement_Hours);

                        // If both conditions are met, add to valid items
                        if (partyCondition && hoursCondition)
                        {
                            validSLAItems.Add(new { SR.WFCaseProgress_ID });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing SLA item for WFCaseProgress_ID: {WFCaseProgress_ID}", SR.WFCaseProgress_ID);
                    }
                }

                countData = validSLAItems;
                foreach (var item in countData)
                {
                    CPIDs = CPIDs + item.WFCaseProgress_ID + ",";
                }
                return CPIDs.TrimEnd(new char[] { ',' });
                ;

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in GetSLAExceeded: {Message}", ex.Message);
                }
            }
            return CPIDs;
        }
        #endregion



        #region ::: Select Case Registration vinay n 14/11/24:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        public async Task<IActionResult> SelectServiceRequest(SelectServiceRequestList Obj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string connString, int LogException) //1--My Que 2--Group 3--All    4--From Summary
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            bool IsAllocate = false;
            int WorkFlowID = await _utilityServiceClient.GetWorkFlowIDAsync("Case Registration", _configuration["DbName"] ?? string.Empty, connString, LogException);
            IEnumerable<WF_WFStepStatus> StepStatus = null;
            List<WF_WFStepStatus> wF_WFStepStatuses = new List<WF_WFStepStatus>();
            List<int> TransactionIDList = new List<int>();
            IQueryable<ServiceRequest> iQServiceReq = null;
            IEnumerable<ServiceRequest> ServiceReq = null;
            IEnumerable<ServiceRequest> ServiceReqAll = null;
            string query = string.Empty;
            string finalquery = string.Empty;
            string wherecondition = string.Empty;
            string AllQueuewherecondition = string.Empty;
            string inCondition = string.Empty;
            var Enquirytype = "";
            bool IsAssignedToFilter = false;
            string AssignedToWhereCondition = string.Empty;
            try
            {

                int userid = Obj.User_ID;
                int Company_ID = Obj.Company_ID;
                string CompanyID = Convert.ToString(Obj.CompanyIDs);
                if (CompanyID != "" && CompanyID != null)
                {
                    CompanyID = Convert.ToString(Obj.CompanyIDs);
                }
                else
                {
                    CompanyID = Convert.ToString(Obj.Company_ID);
                }
                string BranchID = Convert.ToString(Obj.BranchIDs);
                if (BranchID != "" && BranchID != null)
                {
                    BranchID = Convert.ToString(Obj.BranchIDs);
                }
                else
                {
                    BranchID = Convert.ToString(Convert.ToInt32(Obj.Branch));
                }


                int LangID = Obj.Language_ID;
                string GenLangCode = Obj.GeneralLanguageCode.ToString();
                string UserLangCode = Obj.UserLanguageCode.ToString();
                bool IsAdmin = await _utilityServiceClient.CheckForAdminAsync(userid, "Case Registration", _configuration["DbName"] ?? string.Empty, connString, LogException);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string _query = "SELECT * FROM GNM_WFStepStatus";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    WF_WFStepStatus StepStutusObj = new WF_WFStepStatus
                                    {
                                        WFStepStatus_ID = reader.GetInt32(0),
                                        WFStepStatus_Nm = reader.GetString(1),
                                        StepStatusCode = reader.GetString(2)
                                    };

                                    wF_WFStepStatuses.Add(StepStutusObj);
                                }
                                StepStatus = wF_WFStepStatuses.AsEnumerable();
                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in SelectServiceRequest: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                int EndStepTypeID = 0;
                List<int> CloseStepID = new List<int>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string endStepTypeQuery = @"SELECT [WFStepType_ID] 
                                   FROM GNM_WFStepType
                                   WHERE UPPER([WFStepType_Nm]) = 'END'";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(endStepTypeQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            object result = cmd.ExecuteScalar();
                            EndStepTypeID = result != null ? Convert.ToInt32(result) : 0;
                            conn.Close();
                            if (EndStepTypeID > 0)
                            {
                                string closeStepQuery = @"SELECT [WFSteps_ID] 
                                     FROM GNM_WFSteps
                                     WHERE [WFStepType_ID] = @EndStepTypeID AND [WorkFlow_ID] = @WorkFlowID";
                                using (SqlCommand command = new SqlCommand(closeStepQuery, conn))
                                {
                                    command.Parameters.AddWithValue("@EndStepTypeID", EndStepTypeID);
                                    command.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);

                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            CloseStepID.Add(reader.GetInt32(reader.GetOrdinal("WFSteps_ID")));
                                        }
                                    }
                                }
                            }


                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in SelectServiceRequest: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }



                int ClosedStatusID = await _utilityServiceClient.GetEndStepStatusIDAsync(await _utilityServiceClient.GetWorkFlowIDAsync("Case Registration", _configuration["DbName"] ?? string.Empty, connString, LogException), connString, LogException);
                List<int> CloseStepIDStore = new List<int>();
                CloseStepIDStore.AddRange(CloseStepID);
                IsClosed = (ClosedStatusID == Obj.StatusID ? "true" : "false");
                bool isBranchSpecific = false;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string _query = @"SELECT TOP 1 [AllQueue_Filter_IsBranch] 
                         FROM GNM_WorkFlow
                         WHERE [WorkFlow_ID] = @WorkFlowID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            object result = cmd.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
                            {
                                isBranchSpecific = Convert.ToBoolean(result);
                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in SelectServiceRequest: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }


                string statusIds = string.Empty;

                string assign = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Assign").ToString();
                string lockv = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Lock").ToString();
                string unlock = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "UnLock").ToString();
                string myqueue = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "MyQue").ToString();
                string grpQueue = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "GroupQue").ToString();
                string closed = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Closed").ToString();
                string others = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Others").ToString();
                string Yes = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Yes").ToString();
                string No = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "No").ToString();

                string All = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "all").ToString();
                string Created = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Created").ToString();
                string InProgress = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "InProgress").ToString();
                string Closed = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Closed").ToString();
                string Hold = _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "OnHold").ToString();
                int roleID = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string _query = @"SELECT TOP 1 usr.WFRole_ID 
                         FROM GNM_WFRoleUser usr
                         INNER JOIN GNM_WFRole role 
                         ON usr.WFRole_ID = role.WFRole_ID
                         WHERE role.WorkFlow_ID = @WorkFlowID AND usr.UserID = @UserID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);
                            cmd.Parameters.AddWithValue("@UserID", userid);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            object result = cmd.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
                            {
                                roleID = Convert.ToInt32(result);
                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in SelectServiceRequest: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }



                if (Obj.mode == 1)  //My Queue
                {
                    wherecondition = " Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND Addresse_ID =" + userid + " AND Addresse_Flag = 1"; //AND sr.Company_ID in (" + CompanyID+")"; Removed company Id condition for visibilty across companies
                    if (Convert.ToInt32(Obj.FromManager) != 1)// CR 5 changes if count is clicked from Manager Dashboard
                    {

                        IsAllocate = await _utilityServiceClient.CheckAutoAllocationAsync(Company_ID, WorkFlowID, userid, connString, LogException);
                    }
                }

                if (Obj.mode == 2)  //Group Queue
                {
                    inCondition = await _utilityServiceClient.GetGrpQinconditionAsync(userid, connString, LogException);
                    wherecondition = " Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND (Addresse_ID in (" + inCondition + ") AND Addresse_Flag = 0)";// AND sr.Company_ID in (" + CompanyID + ")";
                }
                if (Obj.mode == 3)  //All Queue
                {
                    //if (AllRecords)
                    //{
                    int? parentcompanyId = 0;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string _query = @"SELECT Company_Parent_ID 
                         FROM GNM_Company
                         WHERE Company_ID = @CompanyID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(_query, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@CompanyID", Company_ID);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                object result = cmd.ExecuteScalar();
                                if (result != null && result != DBNull.Value)
                                {
                                    parentcompanyId = Convert.ToInt32(result);
                                }




                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in SelectServiceRequest: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }

                    statusIds = await _utilityServiceClient.GetStatusIDsAsync(ClosedStatusID, WorkFlowID, connString, LogException);
                    if (parentcompanyId == 0)
                    {
                        wherecondition = " (Action_Chosen is null or" + statusIds + ") and  WorkFlow_ID=" + WorkFlowID + " ";//and sr.Company_ID in (" + ParentCompanyIDs + ")";
                    }
                    else
                    {
                        //Commented and added by Harish for displaying records in All queue which are not created in logged in company but has been assigned from Parent or created by logged in company but closed by parent
                        //wherecondition = " (Action_Chosen is null or" + statusIds + ") and  WorkFlow_ID=" + WorkFlowID + " and sr.Company_ID in (" + CompanyID + ")";
                        wherecondition = " (Action_Chosen is null or" + statusIds + ") and  WorkFlow_ID=" + WorkFlowID + " and  Addresse_ID in ( select User_ID from GNM_User where Company_ID=" + CompanyID + ")";
                        AllQueuewherecondition = " (" + statusIds + ") and  WorkFlow_ID=" + WorkFlowID + " and sr.Company_ID in (" + CompanyID + ") and Addresse_ID in ( select User_ID from GNM_User where Company_ID=" + parentcompanyId + ")";
                    }
                }
                if (Obj.mode == 4)  //From Summary
                {
                    int MaxValue = MaxStatusValue;
                    if (Obj.StatusID <= MaxValue)   //If selelected from WorkFlow Status
                    {
                        Obj.mode = 3;
                        statusIds = await _utilityServiceClient.GetStatusIDsAsync(Obj.StatusID, WorkFlowID, connString, LogException);
                        wherecondition = " WorkFlow_ID=" + WorkFlowID + " and sr.Company_ID in (" + CompanyID + ") AND" + statusIds;
                    }
                    else if (Obj.StatusID == (MaxValue + 1)) //If selelected from My Queue
                    {
                        if (Convert.ToInt32(Obj.FromManager) != 1)// CR 5 changes if count is clicked from Manager Dashboard
                        {

                            IsAllocate = await _utilityServiceClient.CheckAutoAllocationAsync(Company_ID, WorkFlowID, userid, connString, LogException);
                        }
                        wherecondition = " Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND Addresse_ID =" + userid + " AND Addresse_Flag = 1 AND sr.Company_ID in (" + CompanyID + ")";
                        Obj.mode = 1;
                    }
                    else if (Obj.StatusID == (MaxValue + 2)) //If selelected from Group Queue
                    {
                        inCondition = await _utilityServiceClient.GetGrpQinconditionAsync(userid, connString, LogException);
                        wherecondition = " Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND Addresse_ID in (" + inCondition + ") AND Addresse_Flag = 0 AND sr.Company_ID in (" + CompanyID + ")";
                        Obj.mode = 2;
                    }
                    else if (Obj.StatusID == (MaxValue + 3)) //If selelected from All Queue
                    {
                        wherecondition = " Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND sr.Company_ID in (" + CompanyID + ")";
                        Obj.mode = 3;
                    }
                    else if (Obj.StatusID == (MaxValue + 4)) //If selelected from slaexceeded
                    {
                        Obj.mode = 3;
                        if (Convert.ToInt32(Obj.FromManager) != 1)// CR 5 changes if count is clicked from Manager Dashboard
                        {
                            string CPIDs = await GetSLAExceeded(Company_ID, WorkFlowID, userid, Convert.ToInt32(Obj.Branch), connString, LogException, _configuration["DbName"] ?? string.Empty);
                            wherecondition = " WFCaseProgress_ID in (" + CPIDs + ")";
                        }
                    }
                }

                string value = string.Empty;

                Mode = Obj.mode;
                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(await _utilityServiceClient.DecryptStringAsync(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                    {
                        for (int i = 0; i < filtersObj.rules.Count(); i++)
                        {
                            if (filtersObj.rules.ElementAt(i).field == "RequestNumber")
                            {
                                filtersObj.rules.ElementAt(i).field = "sr.ServiceRequestNumber";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "ServiceRequestDate")
                            {
                                filtersObj.rules.ElementAt(i).field = "REPLACE(CONVERT(varchar(25),ServiceRequestDate,106),' ','-')";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "IssueArea")
                            {
                                filtersObj.rules.ElementAt(i).field = "issuearea.RefMasterDetail_Name";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "IssueSubArea")
                            {
                                filtersObj.rules.ElementAt(i).field = "IssueSubArea.IssueSubArea_Description";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "Model")
                            {
                                filtersObj.rules.ElementAt(i).field = "model.Model_Name";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "Status")
                            {
                                filtersObj.rules.ElementAt(i).field = "st.WFStepStatus_Nm";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "PartyName")
                            {
                                filtersObj.rules.ElementAt(i).field = "p.Party_Name ";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "Party_Code")
                            {
                                filtersObj.rules.ElementAt(i).field = "p.Party_Code ";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "Product_Unique_Number")
                            {
                                filtersObj.rules.ElementAt(i).field = "PRD.Product_UniqueNo";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "IsDropinString")
                            {
                                filtersObj.rules.ElementAt(i).field = "Dropin.RefMasterDetail_Name";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "EnquiryType")
                            {

                                string[] enquiryType = new string[] { "Support", "Parts", "Service", "Sales" };
                                if (enquiryType[0].ToLower().Contains((filtersObj.rules.ElementAt(i).data).ToLower()))
                                {

                                    value = value + "1" + ',';
                                }
                                if (enquiryType[1].ToLower().Contains((filtersObj.rules.ElementAt(i).data).ToLower()))
                                {
                                    value = value + "2" + ',';
                                }
                                if (enquiryType[2].ToLower().Contains((filtersObj.rules.ElementAt(i).data).ToLower()))
                                {
                                    value = value + "3" + ',';
                                }
                                if (enquiryType[3].ToLower().Contains((filtersObj.rules.ElementAt(i).data).ToLower()))
                                {
                                    value = value + "4" + ',';
                                }
                                value = value.TrimEnd(new char[] { ',' });
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "BranchName")
                            {
                                filtersObj.rules.ElementAt(i).field = "branch.Branch_Name";
                            }

                            if (filtersObj.rules.ElementAt(i).field != "EnquiryType" && filtersObj.rules.ElementAt(i).field != "p.Party_Name " && filtersObj.rules.ElementAt(i).field != "AssignedTo")
                            {
                                if (filtersObj.rules.ElementAt(i).field == "SerialNumber")
                                {
                                    wherecondition = wherecondition + " AND sr.SerialNumber like " + "'%" + filtersObj.rules.ElementAt(i).data + "%'";
                                }
                                else
                                {
                                    wherecondition = wherecondition + " AND " + filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%'";
                                }
                                if (AllQueuewherecondition != string.Empty)
                                    AllQueuewherecondition = AllQueuewherecondition + " AND " + filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%'";
                            }

                            else if (filtersObj.rules.ElementAt(i).field == "EnquiryType")
                            {
                                filtersObj.rules.ElementAt(i).field = "sr.CaseType_ID";
                                wherecondition = wherecondition + " AND " + filtersObj.rules.ElementAt(i).field + " in ('" + value + "') ";
                                if (AllQueuewherecondition != string.Empty)
                                    AllQueuewherecondition = AllQueuewherecondition + " AND " + filtersObj.rules.ElementAt(i).field + " in (" + value + ") ";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "p.Party_Name ")
                            {
                                if (GenLangCode == UserLangCode)
                                {
                                    wherecondition = wherecondition + " AND  (" + filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%')";//or branchdata.Branch_Name  like " + "'%" + filters.rules.ElementAt(i).data + "%'" +
                                    if (AllQueuewherecondition != string.Empty)
                                        AllQueuewherecondition = AllQueuewherecondition + " AND  (" + filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%' )";//or branchdata.Branch_Name  like " + "'%" + filters.rules.ElementAt(i).data + "%'" + "
                                }
                                else
                                {
                                    wherecondition = wherecondition + " AND  (" + "pa.Party_Name " + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%')";//or branchdata.Branch_Name  like " + "'%" + filters.rules.ElementAt(i).data + "%'" +
                                    if (AllQueuewherecondition != string.Empty)
                                        AllQueuewherecondition = AllQueuewherecondition + " AND  (" + "pa.Party_Name " + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%' )";//or branchdata.Branch_Name  like " + "'%" + filters.rules.ElementAt(i).data + "%'" + "
                                }
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "AssignedTo")
                            {
                                IsAssignedToFilter = true;
                                AssignedToWhereCondition = filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%'";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "Product_UniqueNo")
                            {
                                //IsAssignedToFilter = true;
                                AssignedToWhereCondition = filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%'";
                            }

                        }
                    }
                }

                //Advance Search
                if (advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    string op = "";
                    AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();
                    if (advnfilter.rules.Count() > 0)
                    {
                        for (int i = 0; i < advnfilter.rules.Count(); i++)
                        {
                            if (advnfilter.rules.ElementAt(i).Field == "RequestNumber")
                            {
                                advnfilter.rules.ElementAt(i).Field = "sr.ServiceRequestNumber";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "EnquiryType")
                            {
                                string[] enquiryType = new string[] { "Support", "Parts", "Service", "Sales" };
                                if (enquiryType[0].ToLower().Contains((advnfilter.rules.ElementAt(i).Data).ToLower()))
                                {

                                    value = value + "1" + ',';
                                }
                                if (enquiryType[1].ToLower().Contains((advnfilter.rules.ElementAt(i).Data).ToLower()))
                                {
                                    value = value + "2" + ',';
                                }
                                if (enquiryType[2].ToLower().Contains((advnfilter.rules.ElementAt(i).Data).ToLower()))
                                {
                                    value = value + "3" + ',';
                                }
                                if (enquiryType[3].ToLower().Contains((advnfilter.rules.ElementAt(i).Data).ToLower()))
                                {
                                    value = value + "4" + ',';
                                }
                                value = value.TrimEnd(new char[] { ',' });
                            }

                            else if (advnfilter.rules.ElementAt(i).Field == "ServiceRequestDate")
                            {
                                advnfilter.rules.ElementAt(i).Field = "REPLACE(CONVERT(varchar(25),ServiceRequestDate,106),' ','-')";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "IssueArea")
                            {
                                advnfilter.rules.ElementAt(i).Field = "issuearea.RefMasterDetail_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "IssueSubArea")
                            {
                                advnfilter.rules.ElementAt(i).Field = "IssueSubArea.IssueSubArea_Description";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "Model")
                            {
                                advnfilter.rules.ElementAt(i).Field = "model.Model_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "Status")
                            {
                                advnfilter.rules.ElementAt(i).Field = "st.WFStepStatus_Nm";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "PartyName")
                            {
                                advnfilter.rules.ElementAt(i).Field = "p.Party_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "Party_Code")
                            {
                                advnfilter.rules.ElementAt(i).Field = "p.Party_Code";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "BranchName")
                            {
                                advnfilter.rules.ElementAt(i).Field = "branch.Branch_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "SerialNumber")
                            {
                                advnfilter.rules.ElementAt(i).Field = "sr.SerialNumber";
                            }
                            op = getoperator(advnfilter.rules.ElementAt(i).Operator);

                            if (advnfilter.rules.ElementAt(i).Field != "EnquiryType" && advnfilter.rules.ElementAt(i).Field != "p.Party_Name")
                            {
                                if (i == 0)
                                {
                                    if (op == "like")
                                    {
                                        wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%'";
                                        if (AllQueuewherecondition != string.Empty)
                                            AllQueuewherecondition = AllQueuewherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%'";
                                    }
                                    else
                                    {
                                        wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "'";
                                        if (AllQueuewherecondition != string.Empty)
                                            AllQueuewherecondition = AllQueuewherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "'";
                                    }
                                }
                                else
                                {
                                    if (op == "like")
                                    {
                                        wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%'";
                                        if (AllQueuewherecondition != string.Empty)
                                            AllQueuewherecondition = AllQueuewherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%'";
                                    }
                                    else
                                    {
                                        wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "'";
                                        if (AllQueuewherecondition != string.Empty)
                                            AllQueuewherecondition = AllQueuewherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "'";
                                    }
                                }
                            }
                            if (advnfilter.rules.ElementAt(i).Field == "p.Party_Name")
                            {
                                if (i == 0)
                                {
                                    if (op == "like")
                                    {
                                        wherecondition = wherecondition + " AND (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' or branch.Branch_Name" + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' )";
                                        if (AllQueuewherecondition != string.Empty)
                                            AllQueuewherecondition = AllQueuewherecondition + " AND (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' or branchdata.Branch_Name)";
                                    }
                                    else
                                    {
                                        wherecondition = wherecondition + " AND  (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' or branch.Branch_Name" + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' )";
                                        if (AllQueuewherecondition != string.Empty)
                                            AllQueuewherecondition = AllQueuewherecondition + " AND  (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' or branchdata.Branch_Name" + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' )";
                                    }
                                }
                                else
                                {
                                    if (op == "like")
                                    {
                                        wherecondition = wherecondition + " AND  (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' or branch.Branch_Name" + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' )";
                                        if (AllQueuewherecondition != string.Empty)
                                            AllQueuewherecondition = AllQueuewherecondition + " AND (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' or branchdata.Branch_Name" + " " + op + " '%" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "%' )";
                                    }
                                    else
                                    {
                                        wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "'";
                                        if (AllQueuewherecondition != string.Empty)
                                            AllQueuewherecondition = AllQueuewherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data) + "'";
                                    }
                                }
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "EnquiryType")
                            {
                                advnfilter.rules.ElementAt(i).Field = "sr.CaseType_ID";
                                wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " in (" + value + ") ";
                                if (AllQueuewherecondition != string.Empty)
                                    AllQueuewherecondition = AllQueuewherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " in (" + value + ") ";
                            }
                        }
                    }
                }

                if (isBranchSpecific)
                {
                    wherecondition = wherecondition + " AND sr.Branch_ID in (" + BranchID + ")";
                    if (AllQueuewherecondition != string.Empty)
                        AllQueuewherecondition = AllQueuewherecondition + " AND sr.Branch_ID in (" + BranchID + ")";
                }
                string AllQueueQuery = string.Empty;
                if (GenLangCode == UserLangCode)
                {
                    //---------Common From Condition----
                    query = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                    query = query + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID  left outer join GNM_Branch branchdata on branchdata.Branch_ID=sr.Party_ID join GNM_Branch branch on sr.Branch_ID=branch.Branch_ID left outer join GNM_RefMasterDetail issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";
                    query = query + " left outer join GNM_Model model on sr.Model_ID=model.Model_ID left outer join GNM_WFStepStatus st on sr.CallStatus_ID = st.WFStepStatus_ID";
                    query = query + " left outer join HD_IssueSubArea IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID join GNM_RefMasterDetail Dropin on Dropin.REfmasterdetail_ID=sr.ScheduledTYpe_ID ";
                    query = query + " where" + wherecondition;
                    if (Obj.mode == 3 && AllQueuewherecondition != string.Empty)
                    {
                        AllQueueQuery = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                        AllQueueQuery = AllQueueQuery + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID left outer join GNM_Branch branchdata on branchdata.Branch_ID=sr.Party_ID  join GNM_Branch branch on sr.Branch_ID=branch.Branch_ID left outer join GNM_RefMasterDetail issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";
                        AllQueueQuery = AllQueueQuery + " left outer join GNM_Model model on sr.Model_ID=model.Model_ID left outer join GNM_WFStepStatus st on sr.CallStatus_ID = st.WFStepStatus_ID";
                        AllQueueQuery = AllQueueQuery + " left outer join HD_IssueSubArea IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID   join GNM_RefmasterdetailLocale Dropin on Dropin.RefMasterDetail_ID=sr.ScheduledType_ID ";
                        AllQueueQuery = AllQueueQuery + " where" + AllQueuewherecondition;
                    }
                }
                else
                {
                    //---------Common From Condition----
                    query = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID  left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                    query = query + " left outer join GNM_Party pa on sr.Party_ID=pa.Party_ID left outer join GNM_PartyLocale p on p.Party_ID=pa.Party_ID and p.Language_ID=" + LangID + " left outer join GNM_BranchLocale branchdata on branchdata.Branch_ID=sr.Party_ID join GNM_BranchLocale branch on sr.Branch_ID=branch.Branch_ID left outer join GNM_RefMasterDetailLocale issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID and issuearea.Language_ID=" + LangID;
                    query = query + " left outer join GNM_ModelLocale model on sr.Model_ID=model.Model_ID and model.Language_ID=" + LangID + " left outer join GNM_WFStepStatusLocale st on sr.CallStatus_ID = st.WFStepStatus_ID";
                    query = query + " left outer join HD_IssueSubAreaLocale IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID join GNM_RefMasterDetailLocale Dropin on Dropin.REfmasterdetail_ID=sr.ScheduledTYpe_ID";
                    query = query + " where" + wherecondition;
                    if (Obj.mode == 3 && AllQueuewherecondition != string.Empty)
                    {
                        AllQueueQuery = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID ";
                        AllQueueQuery = AllQueueQuery + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID left outer join GNM_PartyLocale pa on p.Party_ID=pa.Party_ID and pa.Language_ID=" + LangID + " left outer join GNM_RefMasterDetailLocale issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID and issuearea.Language_ID=" + LangID;
                        AllQueueQuery = AllQueueQuery + " left outer join GNM_ModelLocale model on sr.Model_ID=model.Model_ID and model.Language_ID=" + LangID + " left outer join GNM_WFStepStatusLocale st on sr.CallStatus_ID = st.WFStepStatus_ID ";
                        AllQueueQuery = AllQueueQuery + " left outer join HD_IssueSubAreaLocale IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID   join GNM_RefmasterdetailLocale Dropin on Dropin.RefMasterDetail_ID=sr.ScheduledType_ID ";
                        AllQueueQuery = AllQueueQuery + " where" + AllQueuewherecondition;
                    }
                }

                //----To get total record count with respect to given filter and search
                finalquery = string.Empty;
                if (!IsAssignedToFilter) finalquery = "select count(sr.ServiceRequest_ID)" + query;
                else finalquery = "select * into t1 from (select ServiceRequest_ID,CallStatus_ID,case when Addresse_Flag=1 then (select User_Name from GNM_User where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRole where WFRole_ID=Addresse_ID) end as AssignedTo " + query + ") as ABC select count(ServiceRequest_ID) from t1 where " + AssignedToWhereCondition;
                // --- All Queue (Closed record Should not come)
                if (Obj.mode == 3 && Obj.Legendfilter == "All")
                {
                    finalquery += "  and CallStatus_ID NOT IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where WFStepStatus_Nm='Closed') ";
                }
                //if (Legendfilter == All) { iQServiceReq = ServiceReq.AsQueryable<ServiceRequest>(); } else
                if (Obj.Legendfilter == Created) { finalquery += "  and CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Created') "; }
                else if (Obj.Legendfilter == InProgress) { finalquery += "  and CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='InProgress') "; }
                else if (Obj.Legendfilter == Closed) { finalquery += "  and CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Closed') "; }
                else if (Obj.Legendfilter == Hold) { finalquery += "  and CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Hold') "; }
                if (IsAssignedToFilter) finalquery += " drop table t1";
                List<int> rcountList = new List<int>();
                IEnumerable<int> rcount = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(finalquery, conn))
                        {
                            cmd.CommandType = CommandType.Text;




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    rcountList.Add(reader.GetInt32(0));
                                }
                                rcount = rcountList.AsEnumerable();
                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in SelectServiceRequest: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                count = rcount.ElementAt(0);
                if (Obj.mode == 3 && AllQueuewherecondition != string.Empty)
                {
                    finalquery = string.Empty;
                    if (!IsAssignedToFilter) finalquery = "select count(sr.ServiceRequest_ID)" + AllQueueQuery;
                    else finalquery = "select * into t1 from (select ServiceRequest_ID,case when Addresse_Flag=1 then (select User_Name from GNM_User where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRole where WFRole_ID=Addresse_ID) end as AssignedTo " + AllQueueQuery + ") as ABC select count(ServiceRequest_ID) from t1 where " + AssignedToWhereCondition + " drop table t1";
                    List<int> AllcountList = new List<int>();
                    IEnumerable<int> Allcount = null;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(finalquery, conn))
                            {
                                cmd.CommandType = CommandType.Text;




                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        AllcountList.Add(reader.GetInt32(0));
                                    }
                                    Allcount = AllcountList.AsEnumerable();
                                }




                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in SelectServiceRequest: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    count = count + Allcount.ElementAt(0);
                }

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                //---To get page wise queue data with respect to given filter and search----



                finalquery = string.Empty;

                if (sidx == "ServiceRequest_ID" || sidx == "RequestNumber") { sidx = "COALESCE(sr.ParentIssue_ID,sr.ServiceRequest_ID)"; }
                else if (sidx == "PartyName") { sidx = "p.Party_Name"; }
                else if (sidx == "Party_Code") { sidx = "p.Party_Code"; }
                else if (sidx == "Model") { sidx = "Model_Name"; }
                else if (sidx == "SerialNumber") { sidx = "sr.SerialNumber"; }
                else if (sidx == "IssueArea") { sidx = "issuearea.RefMasterDetail_Name"; }
                else if (sidx == "Status") { sidx = "st.WFStepStatus_Nm"; }
                else if (sidx == "IsDropinString") { sidx = "Dropin.RefMasterDetail_Name"; }
                else if (sidx == "AssignedTo") { sidx = "case when Addresse_Flag=1 then (select User_Name from GNM_User where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRole where WFRole_ID=Addresse_ID) end"; }
                string Qry = await _utilityServiceClient.GetServiceRequestQueryAsync(connString, LogException, LangID, GenLangCode, UserLangCode, 0, 0, 0, 0, sidx, sord, _configuration["DbName"] ?? string.Empty);
                finalquery = finalquery + Qry + " where" + wherecondition;
                if (Obj.Legendfilter == Created) { finalquery += "  and sr.CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Created') "; }
                else if (Obj.Legendfilter == InProgress) { finalquery += "  and sr.CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='InProgress') "; }
                else if (Obj.Legendfilter == Closed) { finalquery += "  and sr.CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Closed') "; }
                else if (Obj.Legendfilter == Hold) { finalquery += "  and sr.CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Hold') "; }
                if (Obj.mode == 3 && AllQueuewherecondition != string.Empty)
                {
                    AllQueueQuery = string.Empty;
                    if (GenLangCode == UserLangCode)
                    {
                        //---------Common From Condition----
                        AllQueueQuery = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                        AllQueueQuery = AllQueueQuery + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID left outer join GNM_Branch branchdata on branchdata.Branch_ID=sr.Party_ID  join GNM_Branch branch on sr.Branch_ID=branch.Branch_ID left outer join GNM_RefMasterDetail issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";
                        AllQueueQuery = AllQueueQuery + " left outer join GNM_Model model on sr.Model_ID=model.Model_ID LEFT OUTER JOIN GNM_ProductType PT ON sr.ProductType_ID = PT.ProductType_ID left outer join GNM_RefMasterDetail brand on sr.Brand_ID=brand.RefMasterDetail_ID left outer join PST_QuotationHeader quot on sr.Quotation_ID=quot.Quotation_ID  left outer join SRT_JobCardHeader job on sr.JobCard_ID=job.JobCard_ID left outer join GNM_WFStepStatus jst on job.JobCardStatus_ID=jst.WFStepStatus_ID left outer join GNM_WFStepStatus st on sr.CallStatus_ID = st.WFStepStatus_ID";
                        AllQueueQuery = AllQueueQuery + " left outer join HD_IssueSubArea IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID join GNM_RefMasterDetail Dropin on Dropin.REfmasterdetail_ID=sr.ScheduledTYpe_ID left outer join GNM_RefMasterDetail MainGroup on sr.FunctionGroup_ID=MainGroup.RefMasterDetail_ID left outer join GNM_RefMasterDetail SubGroup on sr.SubGroup_ID=SubGroup.RefMasterDetail_ID";
                    }
                    else
                    {
                        //---------Common From Condition----
                        AllQueueQuery = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                        AllQueueQuery = AllQueueQuery + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID left outer join GNM_PartyLocale pa on p.Party_ID=pa.Party_ID and pa.Language_ID=" + LangID + " left outer join GNM_BranchLocale branchdata on branchdata.Branch_ID=sr.Party_ID  join GNM_Branch branch on sr.Branch_ID=branch.Branch_ID  left outer join GNM_RefMasterDetailLocale issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID and issuearea.Language_ID=" + LangID;
                        AllQueueQuery = AllQueueQuery + " left outer join GNM_ModelLocale model on sr.Model_ID=model.Model_ID and model.Language_ID=" + LangID + " LEFT OUTER JOIN GNM_ProductType PT ON sr.ProductType_ID = PT.ProductType_ID left outer join GNM_RefMasterDetail brand on sr.Brand_ID=brand.RefMasterDetail_ID left outer join PST_QuotationHeader quot on sr.Quotation_ID=quot.Quotation_ID  left outer join SRT_JobCardHeader job on sr.JobCard_ID=job.JobCard_ID left outer join GNM_WFStepStatusLocale jst on job.JobCardStatus_ID=jst.WFStepStatus_ID left outer join GNM_WFStepStatusLocale st on sr.CallStatus_ID = st.WFStepStatus_ID";
                        AllQueueQuery = AllQueueQuery + " left outer join HD_IssueSubAreaLocale IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID   join GNM_RefmasterdetailLocale Dropin on Dropin.RefMasterDetail_ID=sr.ScheduledType_ID left outer join GNM_RefMasterDetailLocale MainGroup on sr.FunctionGroup_ID=MainGroup.RefMasterDetail_ID and MainGroup.Language_ID=" + LangID + " left outer join GNM_RefMasterDetailLocale SubGroup on sr.SubGroup_ID=SubGroup.RefMasterDetail_ID and SubGroup.Language_ID=" + LangID;
                    }
                    if (GenLangCode == UserLangCode)
                    {
                        finalquery = finalquery + " union all select branch.Branch_Name AS BranchName,sr.Branch_ID ,sr.IsDealer,sr.IsEscalted,EscalatedLevel, sr.CaseType_ID,EnquiryType=(case when sr.CaseType_ID=1 then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ),sr.ServiceRequest_ID,sr.ServiceRequestNumber as RequestNumber,sr.CallDescription,sr.ChildTicket_Sequence_ID,ProductReading,sr.Party_ID, sr.PartyContactPerson_ID,sr.Model_ID,sr.Brand_ID, sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.IssueSubArea_ID,sr.CallOwner_ID,sr.Region_ID,PT.ProductType_Name AS ProductType,convert(varchar(4),sr.FinancialYear) as FinancialYear,sr.AttachmentCount as Attachmentco,ServiceRequestDate,case when  sr.IsDealer=1 then branchdata.Branch_Name else  p.Party_Name end as PartyName,p.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,MainGroup.RefMasterDetail_Name as FunctionGroupName,SubGroup.RefMasterDetail_Name as SubGroupName,";
                    }
                    else
                    {
                        finalquery = finalquery + " union all select branch.Branch_Name AS BranchName,sr.Branch_ID ,sr.IsDealer, sr.IsEscalted,EscalatedLevel,sr.CaseType_ID,EnquiryType=(case when sr.CaseType_ID=1 then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ),sr.ServiceRequest_ID,sr.ServiceRequestNumber as RequestNumber,sr.CallDescription,sr.ChildTicket_Sequence_ID,ProductReading,sr.Party_ID, sr.PartyContactPerson_ID,sr.Model_ID,sr.Brand_ID, sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.IssueSubArea_ID,sr.CallOwner_ID,sr.Region_ID,PT.ProductType_Name AS ProductType,convert(varchar(4),sr.FinancialYear) as FinancialYear,sr.AttachmentCount as Attachmentco,ServiceRequestDate,case when  sr.IsDealer=1 then branchdata.Branch_Name else  pa.Party_Name end as PartyName,p.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,MainGroup.RefMasterDetail_Name as FunctionGroupName,SubGroup.RefMasterDetail_Name as SubGroupName,";
                    }

                    finalquery = finalquery + "jst.WFStepStatus_Nm as JobCardStatus,sr.Product_ID,sr.IssueArea_ID, Dropin.RefMasterDetail_Name AS IsDropin,Model_Name as Model,sr.SerialNumber,ISNULL(PRD.Product_UniqueNo,'') AS Product_Unique_Number,brand.RefMasterDetail_Name as Brand_Name,st.WFStepStatus_Nm as Status, st.StepStatusCode, Locked_Ind,Addresse_ID,Addresse_Flag, WFNextStep_ID, WFSteps_ID,Action_Chosen,CASE  WHEN Addresse_Flag = 1 THEN (SELECT User_Name FROM GNM_User WHERE User_ID = Addresse_ID)  ELSE (SELECT WFRole_Name FROM GNM_WFRole WHERE WFRole_ID = Addresse_ID)  END AS AssignedTo,'JobCardActivity'=(select top 1 RefMasterDetail_Name from SRT_JobCardActivityDetail as jca inner join GNM_RefMasterDetail as ja on jca.Status_ID=ja.RefMasterDetail_ID where jca.JobCard_ID=job.JobCard_ID order by jca.JobCardActivityDetails_ID desc), quot.QuotationNumber as QuotationNumber,job.JobCardNumber as JobNumber, ";
                    finalquery = finalquery + " ROW_NUMBER() OVER(ORDER BY Transaction_ID desc) as row_number";
                    finalquery = finalquery + AllQueueQuery + " where " + AllQueuewherecondition;
                    if (Obj.Legendfilter == Created) { finalquery += "  and sr.CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Created') "; }
                    else if (Obj.Legendfilter == InProgress) { finalquery += "  and sr.CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='InProgress') "; }
                    else if (Obj.Legendfilter == Closed) { finalquery += "  and sr.CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Closed') "; }
                    else if (Obj.Legendfilter == Hold) { finalquery += "  and sr.CallStatus_ID IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where StepStatusCode='Hold') "; }
                }

                // --- All Queue (Closed record Should not come)
                if (Obj.mode == 3 && Obj.Legendfilter == "All")
                {
                    finalquery += "  and sr.CallStatus_ID NOT IN (select ISNULL(WFStepStatus_ID,0) from GNM_WFStepStatus where WFStepStatus_Nm='Closed') ";
                }

                if (!IsAssignedToFilter) finalquery = finalquery + ") SELECT * FROM NewT WHERE row_number BETWEEN " + (((page - 1) * (rows)) + 1) + " AND " + (page * rows);// + " Order by row_number " + sord;
                else finalquery = finalquery + ") select * into t1 from(SELECT *,ROW_NUMBER() OVER(ORDER BY ServiceRequest_ID desc) as row_number1 FROM NewT WHERE " + AssignedToWhereCondition + ") as x select * from t1 where row_number1 BETWEEN " + (((page - 1) * (rows)) + 1) + " AND " + (page * rows) + " drop table t1";//Order by row_number " + sord + "

                //if (!IsAssignedToFilter) finalquery = finalquery + ") SELECT * FROM NewT  Order by row_number " + sord;
                //else finalquery = finalquery + ") select * into t1 from(SELECT *,ROW_NUMBER() OVER(ORDER BY ServiceRequest_ID desc) as row_number1 FROM NewT WHERE " + AssignedToWhereCondition + ") as x select * from t1  Order by row_number " + sord + " drop table t1";
                List<ServiceRequest> ServiceReqList = new List<ServiceRequest>();
                ServiceReqList = GetValueFromDB<List<ServiceRequest>>(finalquery, null, connString);
                ServiceReq = ServiceReqList.AsEnumerable();



                //----------To store Transaction ID of respective Queue for Navigation---//
                finalquery = string.Empty;
                if (GenLangCode == UserLangCode)
                {
                    finalquery = "select sr.Branch_ID, sr.IsDealer,sr.CaseType_ID, sr.ServiceRequest_ID,ServiceRequestNumber as RequestNumber,ServiceRequestDate,case when sr.IsDealer=1 then branchdata.Branch_Name else P.Party_Name end  as PartyName,P.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,";
                }
                else
                {
                    finalquery = "select sr.Branch_ID, sr.IsDealer,sr.CaseType_ID, sr.ServiceRequest_ID,ServiceRequestNumber as RequestNumber,ServiceRequestDate,case when sr.IsDealer=1 then branchdata.Branch_Name else P.Party_Name end  as PartyName,Pa.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,";
                }
                finalquery = finalquery + "Model_Name as Model,sr.SerialNumber,st.WFStepStatus_Nm as Status,st.StepStatusCode";
                finalquery = finalquery + query;
                // Session["ServiceRequestRowInd"] = finalquery.ToString();

                //----To store filtered data for Export feature---
                string AssignedTo = "";
                if (GenLangCode == UserLangCode)
                { AssignedTo = "case when Addresse_Flag=1 then (select User_Name from GNM_User where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRole where WFRole_ID=Addresse_ID) end as AssignedTo"; }
                else { AssignedTo = "case when Addresse_Flag=1 then (select User_Name from GNM_UserLocale where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRoleLocale where WFRole_ID=Addresse_ID) end as AssignedTo"; }
                finalquery = string.Empty;
                if (!IsAssignedToFilter)
                {
                    if (GenLangCode == UserLangCode)
                    {
                        finalquery = "select * into t1 from (select Row_Number() Over(Order by COALESCE(sr.ParentIssue_ID,sr.ServiceRequest_ID),sr.ChildTicket_Sequence_ID ) as   NewRN,sr.Branch_ID,sr.IsDealer,Enquirytype=(case when sr.CaseType_ID=1  then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ), sr.ServiceRequest_ID,ServiceRequestNumber as RequestNumber,ServiceRequestDate,case when sr.IsDealer=1 then branchdata.Branch_Name else P.Party_Name end as PartyName,P.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,Dropin.RefMasterDetail_Name as IsDropin ,";
                    }
                    else
                    {
                        finalquery = "select * into t1 from (select Row_Number() Over(Order by COALESCE(sr.ParentIssue_ID,sr.ServiceRequest_ID),sr.ChildTicket_Sequence_ID ) as   NewRN,sr.Branch_ID,sr.IsDealer,Enquirytype=(case when sr.CaseType_ID=1  then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ), sr.ServiceRequest_ID,ServiceRequestNumber as RequestNumber,ServiceRequestDate,case when sr.IsDealer=1 then branchdata.Branch_Name else P.Party_Name end as PartyName,Pa.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,Dropin.RefMasterDetail_Name as IsDropin ,";
                    }
                    finalquery = finalquery + "Model_Name as Model,st.WFStepStatus_Nm as Status,st.StepStatusCode,Locked_Ind,Addresse_ID,Addresse_Flag,WFNextStep_ID,sr.Brand_ID,ISNULL(PRD.Product_UniqueNo,'') AS Product_Unique_Number,PRD.Reading_Unit,sr.ProductType_ID,sr.PartyContactPerson_ID,sr.SerialNumber,sr.ProductReading,sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.CallDateAndTime,sr.PromisedCompletionDate,sr.CallDescription,sr.IsUnderWarranty,sr.IsUnderBreakDown,sr.CustomerRating,sr.CallClosureDateAndTime,sr.CallOwner_ID,sr.FunctionGroup_ID,sr.ClosureType_ID,sr.CaseType_ID,sr.InformationCollected,sr.Region_ID,sr.ClosingDescription,sr.Party_ID," + AssignedTo + "";
                    finalquery = finalquery + query + ") as x  Select * from T1  order by NewRN " + sord + " drop table T1";
                }
                else
                {
                    if (GenLangCode == UserLangCode)
                    {
                        finalquery = "select * into t1 from (select Row_Number() Over(Order by COALESCE(sr.ParentIssue_ID,sr.ServiceRequest_ID),sr.ChildTicket_Sequence_ID ) as   NewRN,sr.Branch_ID,sr.IsDealer,Enquirytype=(case when sr.CaseType_ID=1  then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ), sr.ServiceRequest_ID,ServiceRequestNumber as RequestNumber,ServiceRequestDate,case when sr.IsDealer=1 then branchdata.Branch_Name else P.Party_Name end as PartyName,P.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,Dropin.RefMasterDetail_Name as IsDropin ,";
                    }
                    else
                    {
                        finalquery = "select * into t1 from (select Row_Number() Over(Order by COALESCE(sr.ParentIssue_ID,sr.ServiceRequest_ID),sr.ChildTicket_Sequence_ID ) as   NewRN,sr.Branch_ID,sr.IsDealer,Enquirytype=(case when sr.CaseType_ID=1  then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ), sr.ServiceRequest_ID,ServiceRequestNumber as RequestNumber,ServiceRequestDate,case when sr.IsDealer=1 then branchdata.Branch_Name else P.Party_Name end as PartyName,Pa.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,Dropin.RefMasterDetail_Name as IsDropin ,";
                    }
                    finalquery = finalquery + "Model_Name as Model,st.WFStepStatus_Nm as Status,st.StepStatusCode,Locked_Ind,Addresse_ID,Addresse_Flag,WFNextStep_ID,sr.Brand_ID,ISNULL(PRD.Product_UniqueNo,'') AS Product_Unique_Number,PRD.Reading_Unit,sr.ProductType_ID,sr.PartyContactPerson_ID,sr.SerialNumber,sr.ProductReading,sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.CallDateAndTime,sr.PromisedCompletionDate,sr.CallDescription,sr.IsUnderWarranty,sr.IsUnderBreakDown,sr.CustomerRating,sr.CallClosureDateAndTime,sr.CallOwner_ID,sr.FunctionGroup_ID,sr.ClosureType_ID,sr.CaseType_ID,sr.InformationCollected,sr.Region_ID,sr.ClosingDescription,sr.Party_ID," + AssignedTo + "";
                    finalquery = finalquery + query + ") as x select * from t1 where " + AssignedToWhereCondition + " order by NewRN " + sord + " drop table t1";
                }

                ServiceReq = from SR in ServiceReq
                             select new ServiceRequest()
                             {
                                 ServiceRequest_ID = SR.ServiceRequest_ID,
                                 SerialNumber = (SR.SerialNumber == null ? "" : SR.SerialNumber),
                                 Party_Code = SR.Party_Code,
                                 PartyName = SR.PartyName,
                                 IssueArea = SR.IssueArea,
                                 IssueSubArea = SR.IssueSubArea,
                                 Status = SR.Status,
                                 //Modified By Puneeth M On 10-Jan-2015 for QA Issue -->Exported report should be displayed as same as GUI
                                 ServiceRequestDate = SR.ServiceRequestDate,
                                 ServiceRequestDateSTR = SR.ServiceRequestDate.ToString("dd-MMM-yyyy"),
                                 //
                                 RequestNumber = SR.RequestNumber,
                                 Model = SR.Model,
                                 Lock = ((Obj.mode == 1) ? ("") : (Obj.mode == 2) ? ("") : (CloseStepIDStore.Contains((SR.WFNextStep_ID.HasValue ? SR.WFNextStep_ID.Value : 0)) ? closed : (SR.Addresse_ID == userid && SR.Addresse_Flag == 1) ? myqueue : ((SR.Addresse_ID == roleID && SR.Addresse_Flag == 0) ? grpQueue : others))).ToString(),
                                 Locked_Ind = Convert.ToBoolean(SR.Locked_Ind),
                                 IndicatorType = (CloseStepIDStore.Contains((SR.WFNextStep_ID.HasValue ? SR.WFNextStep_ID.Value : 0)) ? 3 : (SR.Addresse_ID == userid && SR.Addresse_Flag == 1) ? 1 : ((SR.Addresse_ID == roleID && SR.Addresse_Flag == 0) ? 2 : 4)),
                                 Attachmentco = SR.Attachmentco,
                                 BranchName = GetBranchName(connString, LogException, SR.Branch_ID),
                                 CaseType_ID = SR.CaseType_ID,
                                 EnquiryType = SR.EnquiryType,
                                 CallDescription = SR.CallDescription,
                                 AssignedTo = SR.StepStatusCode == "Closed" ? "" : SR.AssignedTo,
                                 StepStatusCode = SR.StepStatusCode,
                                 Product_Unique_Number = SR.Product_Unique_Number,
                                 IsDropinString = SR.IsDropin,
                                 IsEscaltedString = Convert.ToBoolean(SR.IsEscalted) == true ? Yes : No,
                                 EscalatedLevel = SR.EscalatedLevel,
                                 FunctionGroupName = SR.FunctionGroupName,
                                 SubGroupName = SR.SubGroupName
                             };

                iQServiceReq = ServiceReq.AsQueryable<ServiceRequest>();
                LandingPageServiceRequestExport = finalquery.ToString();


                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                var unlockVal = await _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "UnLock");

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    Allocate = IsAllocate,
                    rows = (from SR in iQServiceReq
                            select new
                            {
                                ServiceRequest_ID = SR.ServiceRequest_ID,
                                Edit = "<a title=" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "view").ToString() + " href='#' style='font-size: 13px;' key='" + SR.ServiceRequest_ID + "' mode='Read' " + ((Obj.mode == 1) ? " Rmode='MyQ'" : "Rmode='GroupQ'") + "  class='edtSRClick' style='cursor:pointer'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                RequestNumber = SR.RequestNumber,
                                //Modified By Puneeth M On 10-Jan-2015 for QA Issue -->Exported report should be displayed as same as GUI
                                ServiceRequestDate = SR.ServiceRequestDate.ToString("dd-MMM-yyyy"),
                                ServiceRequestDateSTR = SR.ServiceRequestDateSTR,
                                //
                                Party_Code = SR.Party_Code,
                                PartyName = SR.PartyName,
                                IssueArea = SR.IssueArea,
                                Model = SR.Model,
                                IssueSubArea = SR.IssueSubArea,
                                SerialNumber = (SR.SerialNumber == null || SR.SerialNumber == "") ? "" : SR.SerialNumber,
                                Assign = (Obj.mode == 1) ? ((Convert.ToBoolean(SR.Locked_Ind)) ? "<input type='button' class='LockRow' width='50' key='" + SR.ServiceRequest_ID + "' value='" + unlockVal + "' >" : "") : (Obj.mode == 2) ? ("<input type='button' class='LockRow' width='50' key='" + SR.ServiceRequest_ID + "' value='" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "TakeTicket") + "' >") : ((IsAdmin) ? ((SR.IndicatorType == 2 || SR.IndicatorType == 4) ? "<input type='button' class='AssigntoMyQ' width='50' key='" + SR.ServiceRequest_ID + "' value='" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "TakeTicket") + "' >" : "") : ""),
                                Lock = (Obj.mode == 1) ? ("" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "MyQueue") + "") : (Obj.mode == 2) ? ("" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Group") + "") : ((SR.IndicatorType == 1) ? "" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "MyQueue") + "" : (SR.IndicatorType == 2) ? "" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Group") + "" : (SR.IndicatorType == 3) ? "" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Closed") + "" : "" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "Others") + ""),
                                Indicator = "",
                                Status = SR.Status,
                                SR.CallDescription,
                                Attachmentcount = Convert.ToInt32(SR.Attachmentco) > 0 ? "<a title='" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "AttachmentCount").ToString() + "' href='#' style='font-size: 13px;' id='" + SR.ServiceRequest_ID + "'><i class='fa fa-paperclip'></i></a>(" + SR.Attachmentco.ToString() + ")" : "",
                                BranchName = SR.BranchName,
                                CaseType_ID = SR.CaseType_ID,
                                EnquiryType = ("" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), SR.EnquiryType) + ""),
                                SR.AssignedTo,
                                SR.StepStatusCode,
                                SR.Product_Unique_Number,
                                IsDropinString = SR.IsDropinString,

                                IsEscaltedString = Convert.ToBoolean(SR.IsEscalted) == true ? Yes : No,
                                EscalatedLevel = SR.EscalatedLevel,
                                FunctionGroupName = SR.FunctionGroupName,
                                SubGroupName = SR.SubGroupName

                            }).ToList(),
                    filter = filters,
                    Mode = Mode,
                    IsClosed = ClosedStatusID == Obj.StatusID ? "true" : "false",

                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in SelectServiceRequest: {Message}", ex.Message);
                }
            }
            return new JsonResult(jsonobj);
        }
        public string GetBranchName(string connString, int LogException, int Branch_ID)
        {
            string branchName = null;

            using (SqlConnection conn = new SqlConnection(connString))
            {
                string _query = "SELECT TOP 1 Branch_Name FROM GNM_Branch WHERE Branch_ID = @Branch_ID";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(_query, conn))
                    {
                        cmd.CommandType = CommandType.Text;

                        cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                branchName = reader["Branch_Name"] != DBNull.Value ? reader["Branch_Name"].ToString() : null;
                            }



                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        _logger.LogError(ex, "Error in GetBranchName: {Message}", ex.Message);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
                return branchName;
            }
        }
        private static bool ColumnExists(SqlDataReader reader, string columnName)
        {
            try
            {
                return reader.GetOrdinal(columnName) >= 0;
            }
            catch (IndexOutOfRangeException)
            {
                return false;
            }
        }
        #endregion

        public static string getoperator(string operatorString)
        {
            switch (operatorString.ToUpper())
            {
                case "EQ":
                    return "=";
                case "NE":
                    return "<>";
                case "LIKE":
                    return "LIKE";
                case "GT":
                    return ">";
                case "LT":
                    return "<";
                case "GE":
                    return ">=";
                case "LE":
                    return "<=";
                default:
                    throw new ArgumentException("Invalid operator: " + operatorString);
            }
        }

        #region ::: To lock the record  vinay n 15/11/24:::
        /// <summary>
        /// To lock the record
        /// </summary>
        /// <returns>...</returns>
        public async Task<IActionResult> LockRecord(LockRecordList Obj, string connString, int LogException)
        {
            string status = "";
            try
            {
                int CompanyID = Convert.ToInt32(Obj.Company_ID);
                int UserID = Convert.ToInt32(Obj.User_ID);
                status = await _utilityServiceClient.LockRecordAsync(connString, LogException, Obj.UserCulture, Obj.ServiceRequestID, UserID, CompanyID, "Case Registration", _configuration["DbName"], Convert.ToInt32(Obj.Branch));
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand("UPDATE HD_ServiceRequest SET Current_AssignTo = @UserID WHERE ServiceRequest_ID = @ServiceRequestID", conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@UserID", UserID);
                            cmd.Parameters.AddWithValue("@ServiceRequestID", Obj.ServiceRequestID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            cmd.ExecuteNonQuery();




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in LockRecord: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in LockRecord: {Message}", ex.Message);
                }
            }
            return new JsonResult(status);
        }
        #endregion
        #region ::: To unlock the record vinay n 15/11/24:::
        /// <summary>
        /// To unlock the record
        /// </summary>
        /// <returns>...</returns>
        public async Task<IActionResult> UnLockRecord(UnLockRecordList Obj, string connString, int LogException)
        {
            string status = "";
            try
            {
                int CompanyID = Convert.ToInt32(Obj.Company_ID);
                int UserID = Convert.ToInt32(Obj.User_ID);
                int? previousRowRoleAddresse_ID = null;
                int? previousRowNAddresse_ID = null;
                status = await _utilityServiceClient.UnLockRecordAsync(connString, LogException, Obj.UserCulture, Obj.ServiceRequestID, UserID, CompanyID, "Case Registration", _configuration["DbName"], Convert.ToInt32(Obj.Branch));
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;
                    string queryRole = "SELECT TOP 1 Addresse_ID FROM GNM_WFCase_Progress WHERE Transaction_ID = @ServiceRequestID AND Addresse_Flag = @AddresseFlagRole AND WorkFlow_ID = @WorkFlowID ORDER BY WFCaseProgress_ID DESC";
                    try
                    {
                        using (cmd = new SqlCommand(queryRole, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@ServiceRequestID", Obj.ServiceRequestID);
                            cmd.Parameters.AddWithValue("@AddresseFlagRole", 0);
                            cmd.Parameters.AddWithValue("@WorkFlowID", 1);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            var resultRole = cmd.ExecuteScalar();
                            if (resultRole != DBNull.Value)
                            {
                                previousRowRoleAddresse_ID = Convert.ToInt32(resultRole);
                            }




                        }

                        if (previousRowRoleAddresse_ID == null)
                        {
                            string queryN = "SELECT TOP 1 Addresse_ID FROM GNM_WFCase_Progress WHERE Transaction_ID = @ServiceRequestID AND Addresse_Flag = @AddresseFlagN AND WorkFlow_ID = @WorkFlowID ORDER BY WFCaseProgress_ID DESC";
                            using (cmd = new SqlCommand(queryN, conn))
                            {
                                cmd.Parameters.AddWithValue("@ServiceRequestID", Obj.ServiceRequestID);
                                cmd.Parameters.AddWithValue("@AddresseFlagN", 1);
                                cmd.Parameters.AddWithValue("@WorkFlowID", 1);
                                var resultN = cmd.ExecuteScalar();
                                if (resultN != DBNull.Value)
                                {
                                    previousRowNAddresse_ID = Convert.ToInt32(resultN);
                                }
                            }
                            int? Current_AssignTo = previousRowRoleAddresse_ID == null ? (previousRowNAddresse_ID == null ? null : previousRowNAddresse_ID) : previousRowRoleAddresse_ID;
                            if (Current_AssignTo != null)
                            {
                                string updateQuery = "UPDATE HD_ServiceRequest SET Current_AssignTo = @CurrentAssignTo WHERE ServiceRequest_ID = @ServiceRequestID";
                                using (cmd = new SqlCommand(updateQuery, conn))
                                {
                                    cmd.Parameters.AddWithValue("@CurrentAssignTo", Current_AssignTo);
                                    cmd.Parameters.AddWithValue("@ServiceRequestID", Obj.ServiceRequestID);
                                    cmd.ExecuteNonQuery();
                                }

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in UnLockRecord: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }




            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in UnLockRecord: {Message}", ex.Message);
                }
            }
            return new JsonResult(status);
        }
        #endregion

        #region ::: GetCustomerDetails vinay n 19/11/24:::
        /// <summary>
        /// To get customer details
        /// </summary>
        public async Task<IActionResult> GetDataAsync(GetDataUserLindingList Obj, string connString, int LogException)
        {
            bool FilterPartyBasedonCompany = true;
            var jsonData = default(dynamic);
            try
            {
                var like = Obj.starts_with.Trim().ToString();
                List<GNM_Party> PartyList = new List<GNM_Party>();
                List<PartyContactDetails> PartyList1 = new List<PartyContactDetails>();
                List<PartyDetailsAuto> PartyList2 = new List<PartyDetailsAuto>();
                List<GNM_Product> ProductList = new List<GNM_Product>();
                List<GNM_PartyLocale> PartyLocaleList = new List<GNM_PartyLocale>();
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;
                    string query = @"
                            SELECT *
                            FROM 
                                GNM_PartyLocale pl
                            INNER JOIN 
                                GNM_Party p ON pl.Party_ID = p.Party_ID
                            WHERE 
                                pl.Language_ID = @LangID AND p.Party_IsActive = 1";
                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@LangID", Obj.LangID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    GNM_PartyLocale partyLocale = new GNM_PartyLocale
                                    {
                                        Party_Locale_ID = Convert.ToInt32(reader["Party_Locale_ID"]),
                                        Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                        Party_Name = reader["Party_Name"].ToString(),
                                        Party_Location = reader["Party_Location"].ToString(),
                                        Party_PaymentTerms = reader["Party_PaymentTerms"].ToString(),
                                        Language_ID = Convert.ToInt32(reader["Language_ID"]),
                                        Party_Address = reader["Party_Address"].ToString(),
                                        Party_Code = reader["Party_Code"].ToString()
                                    };
                                    PartyLocaleList.Add(partyLocale);
                                }
                            }





                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetData party locale retrieval: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                List<GNM_PartyAddress> PartyAddress = new List<GNM_PartyAddress>();
                List<GNM_PartyAddressLocale> PartyAddressLocale = new List<GNM_PartyAddressLocale>();

                int companyID = Obj.Company_ID;
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;
                    string query = @"
                        SELECT TOP 1 Param_Value
                        FROM GNM_CompParam
                        WHERE Company_ID = @CompanyID AND UPPER(Param_Name) = 'FILTERPARTYBASEDONCOMPANY'";
                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@CompanyID", companyID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            object result = cmd.ExecuteScalar();

                            if (result != null)
                            {
                                string paramValue = result.ToString().ToUpper();
                                FilterPartyBasedonCompany = paramValue == "TRUE";
                            }





                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetData filter party check: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                object[] paramtrs = { like, companyID };
                if (Obj.type != 5)
                {

                    string Query = "SELECT TOP(10) GNM_Party.Party_ID,Party_Name,PartyContactPerson_Mobile,PartyContactPerson_Email,Party_Location,PartyContactPerson_ID,GNM_Party.Party_Code FROM GNM_Party left outer join GNM_PartyContactPersonDetails on GNM_Party.Party_ID=GNM_PartyContactPersonDetails.Party_ID WHERE ";
                    if (Obj.type == 1)
                        Query += " Party_Name ";
                    else if (Obj.type == 2)
                        Query += " PartyContactPerson_Mobile ";
                    else if (Obj.type == 3)
                        Query += " PartyContactPerson_Email ";
                    else if (Obj.type == 4)
                        Query += " Party_Location ";
                    if (Obj.type == 1)
                    {
                        if (FilterPartyBasedonCompany)
                        {
                            Query += "LIKE '%' + @like + '%' AND Company_ID= @companyID  AND Party_IsActive=1 AND PartyType IN (1,2,3)";//Removed Party_IsDefaultContact=1 by DK as bug reported by QA during Helpdesk Implementation
                        }
                        else
                        {
                            Query += "LIKE '%' + @like + '%' AND Party_IsActive=1 AND PartyType IN (1,2,3)";//Removed Party_IsDefaultContact=1 by DK as bug reported by QA during Helpdesk Implementation
                        }
                    }
                    else
                    {
                        if (FilterPartyBasedonCompany)
                        {
                            Query += "LIKE '%' + @like + '%' AND Company_ID=@companyID  AND Party_IsActive=1 AND PartyType IN (1,2,3)";//Removed Party_IsDefaultContact=1 by DK as bug reported by QA during Helpdesk Implementation
                        }
                        else
                        {
                            Query += "LIKE '%' + @like + '%' AND Party_IsActive=1 AND PartyType IN (1,2,3)";
                        }
                    }
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(Query, conn))
                            {
                                cmd.CommandType = CommandType.Text;

                                cmd.Parameters.AddWithValue("@like", like);
                                if (FilterPartyBasedonCompany)
                                    cmd.Parameters.AddWithValue("@companyID", companyID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        PartyContactDetails details = new PartyContactDetails
                                        {
                                            Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                            Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                            PartyContactPerson_Mobile = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_Mobile"))
                                                ? null
                                                : reader.GetString(reader.GetOrdinal("PartyContactPerson_Mobile")),
                                            PartyContactPerson_Email = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_Email"))
                                                ? null
                                                : reader.GetString(reader.GetOrdinal("PartyContactPerson_Email")),
                                            Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location"))
                                                ? null
                                                : reader.GetString(reader.GetOrdinal("Party_Location")),
                                            PartyContactPerson_ID = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_ID"))
                                            ? (int?)null
                                            : reader.GetInt32(reader.GetOrdinal("PartyContactPerson_ID")),

                                            Party_Code = reader.IsDBNull(reader.GetOrdinal("Party_Code"))
                                                ? null
                                                : reader.GetString(reader.GetOrdinal("Party_Code"))
                                        };
                                        PartyList1.Add(details);
                                    }
                                }





                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetData party contact details: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }

                }
                else
                {
                    string ProductQuery = "SELECT TOP(10) Product_ID, Product_SerialNumber,* FROM GNM_Product WHERE IsActive=1 and Company_ID=" + @companyID + " and Product_SerialNumber LIKE '%" + like + "%'";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(ProductQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;

                                cmd.Parameters.Add(new SqlParameter("@companyID", SqlDbType.Int) { Value = companyID });


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        GNM_Product product = new GNM_Product
                                        {
                                            Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                            Product_SerialNumber = reader.GetString(reader.GetOrdinal("Product_SerialNumber")),
                                            Product_UniqueNo = reader.IsDBNull(reader.GetOrdinal("Product_UniqueNo")) ? null : reader.GetString(reader.GetOrdinal("Product_UniqueNo")),
                                            Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                            Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                            ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                            PrimarySegment_ID = reader.IsDBNull(reader.GetOrdinal("PrimarySegment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PrimarySegment_ID")),
                                            SecondarySegment_ID = reader.IsDBNull(reader.GetOrdinal("SecondarySegment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SecondarySegment_ID")),
                                            ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                            ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                            IsComponent = reader.IsDBNull(reader.GetOrdinal("IsComponent")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsComponent")),
                                            Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                            NextServiceType_ID = reader.IsDBNull(reader.GetOrdinal("NextServiceType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("NextServiceType_ID")),
                                            NextServiceDate = reader.IsDBNull(reader.GetOrdinal("NextServiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("NextServiceDate")),
                                            MachineStatus_ID = reader.IsDBNull(reader.GetOrdinal("MachineStatus_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("MachineStatus_ID")),
                                            AverageReadingPerDay = reader.IsDBNull(reader.GetOrdinal("AverageReadingPerDay")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("AverageReadingPerDay")),
                                            CommissioningDate = reader.IsDBNull(reader.GetOrdinal("CommissioningDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("CommissioningDate")),
                                            IsActive = reader.IsDBNull(reader.GetOrdinal("IsActive")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                            Warehouse_ID = reader.IsDBNull(reader.GetOrdinal("Warehouse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Warehouse_ID")),
                                            Parts_ID = reader.IsDBNull(reader.GetOrdinal("Parts_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                            SerialStatus = reader.IsDBNull(reader.GetOrdinal("SerialStatus")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SerialStatus")),
                                            DateOfManufacture = reader.IsDBNull(reader.GetOrdinal("DateOfManufacture")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfManufacture")),
                                            Product_EngineSerialNumber = reader.IsDBNull(reader.GetOrdinal("Product_EngineSerialNumber")) ? null : reader.GetString(reader.GetOrdinal("Product_EngineSerialNumber")),
                                            ServiceCompany = reader.IsDBNull(reader.GetOrdinal("ServiceCompany")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceCompany")),
                                            InvoiceDate = reader.IsDBNull(reader.GetOrdinal("InvoiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("InvoiceDate")),
                                            LandingCost = reader.IsDBNull(reader.GetOrdinal("LandingCost")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("LandingCost")),
                                            WarrantyEndDate = reader.IsDBNull(reader.GetOrdinal("WarrantyEndDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("WarrantyEndDate")),
                                            DateOfSale = reader.IsDBNull(reader.GetOrdinal("DateOfSale")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfSale")),
                                            AttachmentCount = reader.IsDBNull(reader.GetOrdinal("AttachmentCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("AttachmentCount")),
                                            WholeSaleDealerid = reader.IsDBNull(reader.GetOrdinal("WholeSaleDealerid")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WholeSaleDealerid")),
                                            IsWholeSaleUser = reader.IsDBNull(reader.GetOrdinal("IsWholeSaleUser")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsWholeSaleUser")),
                                            ServiceEngineer_ID = reader.IsDBNull(reader.GetOrdinal("ServiceEngineer_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceEngineer_ID")),
                                            Reading_Unit = reader.IsDBNull(reader.GetOrdinal("Reading_Unit")) ? null : reader.GetString(reader.GetOrdinal("Reading_Unit")),
                                            LastServiceBranch = reader.IsDBNull(reader.GetOrdinal("LastServiceBranch")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("LastServiceBranch")),
                                            IsPCPApplicable = reader.IsDBNull(reader.GetOrdinal("IsPCPApplicable")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsPCPApplicable")),
                                            PCPFrequency = reader.IsDBNull(reader.GetOrdinal("PCPFrequency")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("PCPFrequency")),
                                            PCPUsedCount = reader.IsDBNull(reader.GetOrdinal("PCPUsedCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("PCPUsedCount")),
                                            IsClaasMachine = reader.IsDBNull(reader.GetOrdinal("IsClaasMachine")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsClaasMachine")),
                                            Series = reader.IsDBNull(reader.GetOrdinal("Series")) ? null : reader.GetString(reader.GetOrdinal("Series")),
                                            isSeriesAttachmentAdded = reader.IsDBNull(reader.GetOrdinal("isSeriesAttachmentAdded")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("isSeriesAttachmentAdded"))
                                        };
                                        ProductList.Add(product);
                                    }
                                }





                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetData product retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }

                }
                if (Obj.LangID == Convert.ToInt32(Obj.GeneralLanguageID))
                {
                    if (Obj.type != 5)
                    {
                        List<GNM_PartyAddress> PartyAddressList = new List<GNM_PartyAddress>();
                        using (SqlConnection conn = new SqlConnection(connString))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(@"SELECT * FROM GNM_PartyAddress", conn))
                                {
                                    cmd.CommandType = CommandType.Text;




                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_PartyAddress partyAddress = new GNM_PartyAddress
                                            {
                                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID"))


                                            };
                                            PartyAddressList.Add(partyAddress);
                                        }
                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetData party address retrieval: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }
                        jsonData = from a in PartyList1
                                   join
                                       b in PartyAddressList on a.Party_ID equals b.Party_ID into PartyAddrsList
                                   from Paddrfinal in PartyAddrsList.DefaultIfEmpty(new GNM_PartyAddress
                                   {
                                       PartyAddress_Address = ""
                                   })
                                   select new
                                   {
                                       ID = a.Party_ID,
                                       Name = a.Party_Name == null ? "" : a.Party_Name,
                                       Party_Code = a.Party_Code,
                                       Mobile = a.PartyContactPerson_Mobile == null ? "" : a.PartyContactPerson_Mobile,
                                       Email = a.PartyContactPerson_Email == null ? "" : a.PartyContactPerson_Email,
                                       Location = a.Party_Location == null ? "" : a.Party_Location,
                                       Address = Paddrfinal.PartyAddress_Address == null ? "" : Paddrfinal.PartyAddress_Address,
                                       PartyContactPerson_ID = a.PartyContactPerson_ID == null ? 0 : a.PartyContactPerson_ID,
                                       SerialNumber = ""
                                   };
                    }
                    else
                    {
                        List<GNM_ProductCustomer> productCustomerList = new List<GNM_ProductCustomer>();
                        List<GNM_Party> partyList = new List<GNM_Party>();
                        List<GNM_PartyAddress> partyAddressList = new List<GNM_PartyAddress>();
                        using (SqlConnection conn = new SqlConnection(connString))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(@"SELECT * FROM GNM_ProductCustomer", conn))
                                {
                                    cmd.CommandType = CommandType.Text;




                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_ProductCustomer productCustomer = new GNM_ProductCustomer
                                            {
                                                ProductCustomer_ID = reader.GetInt32(reader.GetOrdinal("ProductCustomer_ID")),
                                                Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                ProductCustomer_ToDate = reader.IsDBNull(reader.GetOrdinal("ProductCustomer_ToDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("ProductCustomer_ToDate"))


                                            };
                                            productCustomerList.Add(productCustomer);
                                        }
                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetData product customer retrieval: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }
                        using (SqlConnection conn = new SqlConnection(connString))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(@"SELECT * FROM GNM_Party", conn))
                                {
                                    cmd.CommandType = CommandType.Text;




                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_Party party = new GNM_Party
                                            {
                                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                Party_IsActive = reader.GetBoolean(reader.GetOrdinal("Party_IsActive")),
                                                Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Name")),
                                                Party_Code = reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Code")),
                                                Party_Mobile = reader.IsDBNull(reader.GetOrdinal("Party_Mobile")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Mobile")),
                                                Party_Email = reader.IsDBNull(reader.GetOrdinal("Party_Email")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Email")),
                                                Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Location"))
                                            };
                                            partyList.Add(party);
                                        }
                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetData party retrieval: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }
                        using (SqlConnection conn = new SqlConnection(connString))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(@"SELECT * FROM GNM_PartyAddress", conn))
                                {
                                    cmd.CommandType = CommandType.Text;




                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_PartyAddress partyAddress = new GNM_PartyAddress
                                            {
                                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                PartyAddress_Address = reader.IsDBNull(reader.GetOrdinal("PartyAddress_Address")) ? string.Empty : reader.GetString(reader.GetOrdinal("PartyAddress_Address")),
                                                IsDefault = reader.GetBoolean(reader.GetOrdinal("IsDefault"))
                                            };
                                            partyAddressList.Add(partyAddress);
                                        }
                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetData party address locale retrieval: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }
                        jsonData = (from a in ProductList
                                    join c in productCustomerList on a.Product_ID equals c.Product_ID into pclist
                                    from d in pclist.DefaultIfEmpty(new GNM_ProductCustomer
                                    {
                                        ProductCustomer_ID = 0
                                    })
                                    join e in partyList on d.Party_ID equals e.Party_ID
                                    join f in partyAddressList on d.Party_ID equals f.Party_ID into PartyAddrsList
                                    from Paddrfinal in PartyAddrsList.DefaultIfEmpty(new GNM_PartyAddress
                                    {
                                        PartyAddress_Address = ""
                                    })
                                    where e.Party_IsActive == true && d.ProductCustomer_ToDate == null && Paddrfinal.IsDefault == true
                                    select new
                                    {
                                        ID = a.Party_ID,
                                        Name = e.Party_Name == null ? "" : e.Party_Name,
                                        Party_Code = e.Party_Code,
                                        Mobile = e.Party_Mobile == null ? "" : e.Party_Mobile,
                                        Email = e.Party_Email == null ? "" : e.Party_Email,
                                        Location = e.Party_Location == null ? "" : e.Party_Location,
                                        Address = Paddrfinal.PartyAddress_Address == null ? "" : Paddrfinal.PartyAddress_Address,
                                        SerialNumber = a.Product_SerialNumber,
                                        Product_ID = a.Product_ID,
                                        PartyContactPerson_ID = 0
                                    });
                    }
                }
                else
                {
                    if (Obj.type != 5)
                    {
                        if (Obj.type == 1)
                        {
                            string Query = "select TOP(10) b.Party_ID ID,b.Party_Name Name,ISNULL(a.Party_Mobile,'') Mobile,ISNULL(a.Party_Email,'') Email,ISNULL(b.Party_Location,'')Location,ISNULL(d.PartyAddressLocale_Address,'')Address,a.Party_Code from GNM_Party a ";
                            Query += " left outer join GNM_PartyLocale b  on a.Party_ID =b.Party_ID join GNM_PartyAddress c on a.Party_ID=c.Party_ID left outer join GNM_PartyAddressLocale d on c.PartyAddress_ID =d.PartyAddress_ID ";
                            Query += " where a.Company_ID=" + companyID + " and a.Party_IsActive=1 and b.Language_ID=" + Obj.LangID + " and b.Party_Name like '%" + like + "%' ";
                            using (SqlConnection conn = new SqlConnection(connString))
                            {


                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(Query, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;




                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }

                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                PartyDetailsAuto partyDetails = new PartyDetailsAuto
                                                {
                                                    ID = reader["ID"] != DBNull.Value ? Convert.ToInt32(reader["ID"]) : 0,
                                                    Name = reader["Name"] != DBNull.Value ? reader["Name"].ToString() : string.Empty,
                                                    Mobile = reader["Mobile"] != DBNull.Value ? reader["Mobile"].ToString() : string.Empty,
                                                    Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : string.Empty,
                                                    Location = reader["Location"] != DBNull.Value ? reader["Location"].ToString() : string.Empty,
                                                    Address = reader["Address"] != DBNull.Value ? reader["Address"].ToString() : string.Empty,
                                                    Party_Code = reader["Party_Code"] != DBNull.Value ? reader["Party_Code"].ToString() : string.Empty
                                                };
                                                PartyList2.Add(partyDetails);
                                            }
                                        }





                                    }


                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        _logger.LogError(ex, "Error in GetData locale party details: {Message}", ex.Message);
                                    }

                                }
                                finally
                                {
                                    cmd.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }

                            }

                        }
                        else if (Obj.type == 4)
                        {
                            string Query = "select TOP(10) b.Party_ID ID,b.Party_Name Name,ISNULL(a.Party_Mobile,'') Mobile,ISNULL(a.Party_Email,'') Email,ISNULL(b.Party_Location,'')Location,ISNULL(d.PartyAddressLocale_Address,'')Address,a.Party_Code from GNM_Party a ";
                            Query += " left outer join GNM_PartyLocale b  on a.Party_ID =b.Party_ID join GNM_PartyAddress c on a.Party_ID=c.Party_ID left outer join GNM_PartyAddressLocale d on c.PartyAddress_ID =d.PartyAddress_ID ";
                            Query += " where a.Company_ID=" + companyID + " and a.Party_IsActive=1 and b.Language_ID=" + Obj.LangID + " and b.Party_Location like '%" + like + "%' ";
                            using (SqlConnection conn = new SqlConnection(connString))
                            {


                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(Query, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;




                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }

                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                PartyDetailsAuto partyDetails = new PartyDetailsAuto
                                                {
                                                    ID = reader["ID"] != DBNull.Value ? Convert.ToInt32(reader["ID"]) : 0,
                                                    Name = reader["Name"] != DBNull.Value ? reader["Name"].ToString() : string.Empty,
                                                    Mobile = reader["Mobile"] != DBNull.Value ? reader["Mobile"].ToString() : string.Empty,
                                                    Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : string.Empty,
                                                    Location = reader["Location"] != DBNull.Value ? reader["Location"].ToString() : string.Empty,
                                                    Address = reader["Address"] != DBNull.Value ? reader["Address"].ToString() : string.Empty,
                                                    Party_Code = reader["Party_Code"] != DBNull.Value ? reader["Party_Code"].ToString() : string.Empty
                                                };
                                                PartyList2.Add(partyDetails);
                                            }
                                        }





                                    }


                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        _logger.LogError(ex, "Error in GetData locale party details 2: {Message}", ex.Message);
                                    }

                                }
                                finally
                                {
                                    cmd.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }

                            }

                        }

                        jsonData = from b in PartyList2
                                   select new
                                   {
                                       ID = b.ID,
                                       Name = b.Name,
                                       Party_Code = b.Party_Code,
                                       Mobile = b.Mobile,
                                       Email = b.Email,
                                       Location = b.Location,
                                       Address = b.Address,
                                       PartyContactPerson_ID = 0,
                                       SerialNumber = ""
                                   };
                    }
                    else
                    {
                        List<GNM_ProductCustomer> productCustomerList = new List<GNM_ProductCustomer>();
                        List<GNM_Party> partyList = new List<GNM_Party>();
                        List<GNM_PartyAddress> partyAddressList = new List<GNM_PartyAddress>();
                        using (SqlConnection conn = new SqlConnection(connString))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(@"SELECT * FROM GNM_ProductCustomer", conn))
                                {
                                    cmd.CommandType = CommandType.Text;




                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_ProductCustomer productCustomer = new GNM_ProductCustomer
                                            {
                                                ProductCustomer_ID = reader.GetInt32(reader.GetOrdinal("ProductCustomer_ID")),
                                                Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                ProductCustomer_ToDate = reader.IsDBNull(reader.GetOrdinal("ProductCustomer_ToDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("ProductCustomer_ToDate"))


                                            };
                                            productCustomerList.Add(productCustomer);
                                        }
                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetData product customer locale retrieval: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }
                        using (SqlConnection conn = new SqlConnection(connString))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(@"SELECT * FROM GNM_Party", conn))
                                {
                                    cmd.CommandType = CommandType.Text;




                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_Party party = new GNM_Party
                                            {
                                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                Party_IsActive = reader.GetBoolean(reader.GetOrdinal("Party_IsActive")),
                                                Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Name")),
                                                Party_Code = reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Code")),
                                                Party_Mobile = reader.IsDBNull(reader.GetOrdinal("Party_Mobile")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Mobile")),
                                                Party_Email = reader.IsDBNull(reader.GetOrdinal("Party_Email")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Email")),
                                                Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Location"))
                                            };
                                            partyList.Add(party);
                                        }
                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetData party locale retrieval: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }
                        using (SqlConnection conn = new SqlConnection(connString))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(@"SELECT * FROM GNM_PartyAddress", conn))
                                {
                                    cmd.CommandType = CommandType.Text;




                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_PartyAddress partyAddress = new GNM_PartyAddress
                                            {
                                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                PartyAddress_Address = reader.IsDBNull(reader.GetOrdinal("PartyAddress_Address")) ? string.Empty : reader.GetString(reader.GetOrdinal("PartyAddress_Address")),
                                                IsDefault = reader.GetBoolean(reader.GetOrdinal("IsDefault"))
                                            };
                                            partyAddressList.Add(partyAddress);
                                        }
                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetData party address locale retrieval 2: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }
                        jsonData = (from a in ProductList
                                    join c in productCustomerList on a.Product_ID equals c.Product_ID into pclist
                                    from d in pclist.DefaultIfEmpty(new GNM_ProductCustomer
                                    {
                                        ProductCustomer_ID = 0
                                    })
                                    join e in partyList on d.Party_ID equals e.Party_ID
                                    join g in PartyLocaleList on d.Party_ID equals g.Party_ID
                                    join f in partyAddressList on d.Party_ID equals f.Party_ID into PartyAddrsList
                                    from Paddrfinal in PartyAddrsList.DefaultIfEmpty(new GNM_PartyAddress
                                    {
                                        PartyAddress_Address = ""
                                    })
                                    where e.Party_IsActive == true && d.ProductCustomer_ToDate == null && Paddrfinal.IsDefault == true && g.Language_ID == Obj.LangID
                                    select new
                                    {
                                        ID = a.Party_ID,
                                        Name = g.Party_Name == null ? "" : g.Party_Name,
                                        Party_Code = e.Party_Code,
                                        Mobile = e.Party_Mobile == null ? "" : e.Party_Mobile,
                                        Email = e.Party_Email == null ? "" : e.Party_Email,
                                        Location = e.Party_Location == null ? "" : e.Party_Location,
                                        Address = Paddrfinal.PartyAddress_Address == null ? "" : Paddrfinal.PartyAddress_Address,
                                        SerialNumber = a.Product_SerialNumber,
                                        Product_ID = a.Product_ID,
                                        PartyContactPerson_ID = 0
                                    });
                    }
                }
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in GetData: {Message}", ex.Message);
                }
                return new JsonResult(jsonData);
            }
        }
        #endregion

        #region ::: GetDealerDetails vinay n 19/11/24:::
        /// <summary>
        /// To get customer details
        /// </summary>
        public async Task<IActionResult> GetDealerData(GetDealerDataList Obj, string connString, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                var like = Obj.starts_with.Trim().ToString();
                List<GNM_Branch> branchist = new List<GNM_Branch>();
                List<GNM_Product> ProductList = new List<GNM_Product>();

                int companyID = Obj.Company_ID;
                object[] paramtrs = { like, companyID };
                if (Obj.type != 5)
                {

                    string Query = "select  *  From GNM_Branch where ";
                    if (Obj.type == 1)
                        Query += " Branch_Name ";
                    else if (Obj.type == 2)
                        Query += " Branch_Mobile ";
                    else if (Obj.type == 3)
                        Query += " Branch_Email ";
                    else if (Obj.type == 4)
                        Query += " Branch_Location ";

                    Query += "LIKE '%' + @like + '%'  AND Branch_Active=1 ";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(Query, conn))
                            {
                                cmd.CommandType = CommandType.Text;

                                cmd.Parameters.AddWithValue("@like", like);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        GNM_Branch branch = new GNM_Branch
                                        {
                                            Branch_ID = reader["Branch_ID"] != DBNull.Value ? Convert.ToInt32(reader["Branch_ID"]) : 0,
                                            Branch_Name = reader["Branch_Name"] != DBNull.Value ? reader["Branch_Name"].ToString() : string.Empty,
                                            Branch_Mobile = reader["Branch_Mobile"] != DBNull.Value ? reader["Branch_Mobile"].ToString() : string.Empty,
                                            Branch_Email = reader["Branch_Email"] != DBNull.Value ? reader["Branch_Email"].ToString() : string.Empty,
                                            Branch_Location = reader["Branch_Location"] != DBNull.Value ? reader["Branch_Location"].ToString() : string.Empty,
                                            Branch_Address = reader["Branch_Address"] != DBNull.Value ? reader["Branch_Address"].ToString() : string.Empty
                                        };
                                        branchist.Add(branch);
                                    }
                                }





                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetDealerData branch retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }


                }
                else
                {
                    string ProductQuery = "SELECT TOP(10) Product_ID, Product_SerialNumber,* FROM GNM_Product WHERE IsActive=1 and Company_ID=@companyID  and Product_SerialNumber LIKE '%' + @like + '%'";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(ProductQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@companyID", companyID);
                                cmd.Parameters.AddWithValue("@like", like);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        GNM_Product product = new GNM_Product
                                        {
                                            Product_ID = reader["Product_ID"] != DBNull.Value ? Convert.ToInt32(reader["Product_ID"]) : 0,
                                            Product_SerialNumber = reader["Product_SerialNumber"] != DBNull.Value ? reader["Product_SerialNumber"].ToString() : string.Empty,
                                            // Map additional columns as needed
                                        };
                                        ProductList.Add(product);
                                    }
                                }





                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetDealerData product retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }

                }
                if (Obj.LangID == Convert.ToInt32(Obj.GeneralLanguageID))
                {
                    if (Obj.type != 5)
                    {
                        jsonData = from a in branchist

                                   select new
                                   {
                                       ID = a.Branch_ID,
                                       Name = a.Branch_Name == null ? "" : a.Branch_Name,
                                       Mobile = a.Branch_Mobile == null ? "" : a.Branch_Mobile,
                                       Email = a.Branch_Email == null ? "" : a.Branch_Email,
                                       Location = a.Branch_Location == null ? "" : a.Branch_Location,
                                       Address = a.Branch_Address == null ? "" : a.Branch_Address,
                                       PartyContactPerson_ID = 0
                                   };
                    }
                    else
                    {
                        jsonData = (from a in ProductList

                                    select new
                                    {
                                        ID = 0,
                                        Name = "",
                                        Mobile = "",
                                        Email = "",
                                        Location = "",
                                        Address = "",
                                        SerialNumber = a.Product_SerialNumber,
                                        Product_ID = a.Product_ID,
                                        PartyContactPerson_ID = 0
                                    });
                    }
                }

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in GetDealerData: {Message}", ex.Message);
                }
                return new JsonResult(jsonData);
            }

        }
        #endregion

        #region checkDuplicateContactPerson vinay n 19/11/24
        /// <summary>
        /// checkDuplicateContactPerson
        /// </summary>
        /// <param name="CPName"></param>
        /// <param name="Party_ID"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> checkDuplicateContactPersonAsync(checkDuplicateContactPersonList Obj,/*string CPName, int Party_ID,*/string connString, int LogException)
        {
            string res = "0";
            string val = await _utilityServiceClient.DecryptStringAsync(Obj.CPName);
            string CPName = Uri.UnescapeDataString(val);
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = @"
                    SELECT TOP 1 1
                    FROM GNM_PartyContactPersonDetails
                    WHERE UPPER(PartyContactPerson_Name) = UPPER(@CPName)
                    AND Party_ID = @Party_ID";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;

                        cmd.Parameters.AddWithValue("@CPName", CPName);
                        cmd.Parameters.AddWithValue("@Party_ID", Obj.Party_ID);
                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        object result = cmd.ExecuteScalar();

                        if (result != null)
                        {
                            res = "1";
                        }



                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        _logger.LogError(ex, "Error in checkDuplicateContactPerson: {Message}", ex.Message);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }

            return new JsonResult(res);
        }
        #endregion

        #region GetProductDetails vinay n 19/11/24
        public async Task<IActionResult> GetProductDetails(GetProductDetailsUserLandingList Obj, string connString, int LogException)
        {
            var jsondata = default(dynamic);
            GNM_Product productdata = null;
            string modelName = null;
            string brandName = null;
            string productTypeName = null;
            try
            {
                int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {

                    string productQuery = @"
                    SELECT Product_ID, Model_ID, Brand_ID, ProductType_ID, Product_SerialNumber
                    FROM GNM_Product
                    WHERE Product_ID = @ProductID";
                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(productQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@ProductID", Obj.ProductID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    productdata = new GNM_Product
                                    {
                                        Product_ID = Convert.ToInt32(reader["Product_ID"]),
                                        Model_ID = Convert.ToInt32(reader["Model_ID"]),
                                        Brand_ID = Convert.ToInt32(reader["Brand_ID"]),
                                        ProductType_ID = Convert.ToInt32(reader["ProductType_ID"]),
                                        Product_SerialNumber = reader["Product_SerialNumber"].ToString()
                                    };
                                }
                            }





                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetProductDetails product retrieval: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                if (productdata != null)
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {

                        string modelNameQuery = userLanguageID == generalLanguageID
                   ? "SELECT Model_Name FROM GNM_Model WHERE Model_ID = @ModelID"
                   : "SELECT Model_Name FROM GNM_ModelLocale WHERE Model_ID = @ModelID AND Language_ID = @LanguageID";
                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(modelNameQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;

                                cmd.Parameters.AddWithValue("@ModelID", productdata.Model_ID);
                                if (userLanguageID != generalLanguageID)
                                    cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                modelName = cmd.ExecuteScalar()?.ToString();




                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetProductDetails model name retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    using (SqlConnection conn = new SqlConnection(connString))
                    {

                        string brandNameQuery = userLanguageID == generalLanguageID
                    ? "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @BrandID"
                    : "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMasterDetail_ID = @BrandID AND Language_ID = @LanguageID";
                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(brandNameQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;

                                cmd.Parameters.AddWithValue("@BrandID", productdata.Brand_ID);
                                if (userLanguageID != generalLanguageID)
                                    cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                brandName = cmd.ExecuteScalar()?.ToString();




                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetProductDetails brand name retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    using (SqlConnection conn = new SqlConnection(connString))
                    {

                        string productTypeNameQuery = userLanguageID == generalLanguageID
                       ? "SELECT ProductType_Name FROM GNM_ProductType WHERE ProductType_ID = @ProductTypeID"
                       : "SELECT ProductType_Name FROM GNM_ProductTypeLocale WHERE ProductType_ID = @ProductTypeID AND Language_ID = @LanguageID";
                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(productTypeNameQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;

                                cmd.Parameters.AddWithValue("@ProductTypeID", productdata.ProductType_ID);
                                if (userLanguageID != generalLanguageID)
                                    cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                productTypeName = cmd.ExecuteScalar()?.ToString();




                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetProductDetails product type retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    jsondata = new
                    {
                        ModelID = productdata.Model_ID,
                        ModelName = modelName,
                        BrandID = productdata.Brand_ID,
                        BrandName = brandName,
                        ProductTypeID = productdata.ProductType_ID,
                        ProductTypeName = productTypeName,
                        SerialNumber = productdata.Product_SerialNumber
                    };
                }
                else
                {
                    jsondata = new
                    {
                        ModelID = 0,
                        ModelName = "",
                        BrandID = 0,
                        BrandName = "",
                        ProductTypeID = 0,
                        ProductTypeName = "",
                        SerialNumber = ""
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in GetProductDetails: {Message}", ex.Message);
                }
                return null;
            }
            return new JsonResult(jsondata);
        }
        #endregion


        #region ::: SaveCustomerDetails vinay n 19/11/24:::
        /// <summary>
        /// To save customer details
        /// </summary>
        public async Task<IActionResult> SaveCustomer(SaveCustomerList Obj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            int partyID = 0;
            GNM_Party Party = new GNM_Party();
            GNM_PartyAddress PartyAddress = new GNM_PartyAddress();
            GNM_PartyTaxDetails PartyTax = new GNM_PartyTaxDetails();
            GNM_Party gnmPrty = JObject.Parse(Obj.key).ToObject<GNM_Party>();
            GNM_PartyAddress gnmPartyAddress = JObject.Parse(Obj.PartyAddress).ToObject<GNM_PartyAddress>();
            GNM_PartyTaxDetails gnmPartyTax = JObject.Parse(Obj.PartyTaxDetails).ToObject<GNM_PartyTaxDetails>();
            string Msg = string.Empty;
            SqlTransaction transaction = null;
            SqlTransaction transaction2 = null;
            try
            {
                //Added by Venkateshwari for HelpDesk CR-5 Changes 13-Aug-2015 -- Start
                int branchIDForLocalTime = Convert.ToInt32(Obj.Branch.ToString());
                DateTime localTime = await _utilityServiceClient.LocalTimeBasedOnBranchAsync(branchIDForLocalTime, DateTime.Now, connString);
                //Added by Venkateshwari for HelpDesk CR-5 Changes 13-Aug-2015 -- End

                using (SqlConnection conn = new SqlConnection(connString))
                {

                    string insertPartyQuery = @"
                    INSERT INTO GNM_Party (Party_Name, Party_Location, Party_Phone, Party_Email, Party_Fax, Party_Mobile, Party_IsActive, Party_IsLocked, 
                                           Party_PaymentTerms, PartyType, ModifiedBY, ModifiedDate, Country_ID, State_ID, Region_ID, Company_ID)
                    OUTPUT INSERTED.Party_ID
                    VALUES (@PartyName, @PartyLocation, @PartyPhone, @PartyEmail, @PartyFax, @PartyMobile, @IsActive, @IsLocked, @PaymentTerms, @PartyType,
                            @ModifiedBy, @ModifiedDate, @CountryID, @StateID, @RegionID, @CompanyID)";
                    SqlCommand cmd = null;
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }
                    transaction = conn.BeginTransaction();
                    try
                    {
                        using (cmd = new SqlCommand(insertPartyQuery, conn, transaction))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@PartyName", await _utilityServiceClient.DecryptStringAsync(gnmPrty.Party_Name));
                            cmd.Parameters.AddWithValue("@PartyLocation", await _utilityServiceClient.DecryptStringAsync(gnmPrty.Party_Location));
                            cmd.Parameters.AddWithValue("@PartyPhone", gnmPrty.Party_Phone);
                            cmd.Parameters.AddWithValue("@PartyEmail", await _utilityServiceClient.DecryptStringAsync(gnmPrty.Party_Email));
                            cmd.Parameters.AddWithValue("@PartyFax", gnmPrty.Party_Fax);
                            cmd.Parameters.AddWithValue("@PartyMobile", gnmPrty.Party_Mobile);
                            cmd.Parameters.AddWithValue("@IsActive", true);
                            cmd.Parameters.AddWithValue("@IsLocked", false);
                            cmd.Parameters.AddWithValue("@PaymentTerms", await _utilityServiceClient.DecryptStringAsync(gnmPrty.Party_PaymentTerms));
                            cmd.Parameters.AddWithValue("@PartyType", 1); // Customer
                            cmd.Parameters.AddWithValue("@ModifiedBy", Obj.User_ID);
                            cmd.Parameters.AddWithValue("@ModifiedDate", localTime);
                            cmd.Parameters.AddWithValue("@CountryID", gnmPrty.Country_ID);
                            cmd.Parameters.AddWithValue("@StateID", gnmPrty.State_ID);
                            cmd.Parameters.AddWithValue("@RegionID", ((gnmPrty.Region_ID == 0 || gnmPrty.Region_ID == -1) ? null : gnmPrty.Region_ID));
                            cmd.Parameters.AddWithValue("@CompanyID", Obj.Company_ID);



                            partyID = (int)cmd.ExecuteScalar();

                            transaction.Commit();



                        }


                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in SaveCustomer party insertion: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }




                // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID), Convert.ToInt32(Obj.Branch), Convert.ToInt32(Obj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Party.Party_ID, 0, 0, "Insert", false, Convert.ToInt32(Obj.MenuID));
                if (partyID > 0)
                {
                    if (gnmPartyTax.PartyTax_TaxCode != string.Empty && gnmPartyTax.PartyTax_TaxCodeDescription != string.Empty)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {

                            string insertTaxQuery = @"
                        INSERT INTO GNM_PartyTaxDetails (Party_ID, PartyTax_TaxCode, PartyTax_TaxCodeDescription, PartyTaxDetails_IsActive)
                        VALUES (@PartyID, @TaxCode, @TaxCodeDescription, @IsActive)";
                            SqlCommand cmd = null;
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            transaction2 = conn.BeginTransaction();
                            try
                            {
                                using (cmd = new SqlCommand(insertTaxQuery, conn, transaction2))
                                {
                                    cmd.CommandType = CommandType.Text;

                                    cmd.Parameters.AddWithValue("@PartyID", partyID);
                                    cmd.Parameters.AddWithValue("@TaxCode", await _utilityServiceClient.DecryptStringAsync(gnmPartyTax.PartyTax_TaxCode));
                                    cmd.Parameters.AddWithValue("@TaxCodeDescription", await _utilityServiceClient.DecryptStringAsync(gnmPartyTax.PartyTax_TaxCodeDescription));
                                    cmd.Parameters.AddWithValue("@IsActive", true);



                                    cmd.ExecuteNonQuery();





                                }


                            }
                            catch (Exception ex)
                            {
                                transaction.Rollback();
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in SaveCustomer party tax insertion: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }

                    }
                    using (SqlConnection conn = new SqlConnection(connString))
                    {

                        string insertAddressQuery = @"
                    INSERT INTO GNM_PartyAddress (Party_ID, PartyAddress_Location, PartyAddress_Address, PartyAddress_LeadTimeInDays, 
                                                  PartyAddress_CountryID, PartyAddress_StateID, PartyAddress_Active, PartyAddress_ZIP, Region_ID, IsDefault)
                    VALUES (@PartyID, @AddressLocation, @Address, @LeadTime, @CountryID, @StateID, @IsActive, @ZIP, @RegionID, @IsDefault)";
                        SqlCommand cmd = null;
                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        transaction2 = conn.BeginTransaction();
                        try
                        {
                            using (cmd = new SqlCommand(insertAddressQuery, conn, transaction2))
                            {
                                cmd.CommandType = CommandType.Text;

                                cmd.Parameters.AddWithValue("@PartyID", partyID);
                                cmd.Parameters.AddWithValue("@AddressLocation", await _utilityServiceClient.DecryptStringAsync(gnmPrty.Party_Location));
                                cmd.Parameters.AddWithValue("@Address", await _utilityServiceClient.DecryptStringAsync(gnmPartyAddress.PartyAddress_Address));
                                cmd.Parameters.AddWithValue("@LeadTime", 0);
                                cmd.Parameters.AddWithValue("@CountryID", gnmPartyAddress.PartyAddress_CountryID);
                                cmd.Parameters.AddWithValue("@StateID", gnmPartyAddress.PartyAddress_StateID);
                                cmd.Parameters.AddWithValue("@IsActive", true);
                                cmd.Parameters.AddWithValue("@ZIP", gnmPartyAddress.PartyAddress_ZIP);
                                cmd.Parameters.AddWithValue("@RegionID", (object)gnmPrty.Region_ID ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@IsDefault", true);



                                cmd.ExecuteNonQuery();


                                transaction2.Commit();


                            }


                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in SaveCustomer party address insertion: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }

                    // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID), Convert.ToInt32(Obj.Branch), Convert.ToInt32(Obj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Party.Party_ID, 0, 0, "Update", false, Convert.ToInt32(Obj.MenuID));
                }
                jsonResult = new
                {
                    Party_ID = partyID,
                    IsSuccess = true,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in SaveCustomer: {Message}", ex.Message);
                }
                Msg = string.Empty;
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: GetCallHistory vinay n 19/11/24:::
        /// <summary>
        /// To get Case history
        /// </summary>
        /// 
        public async Task<IActionResult> GetCallHistory(GetCallHistoryList Obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool advnce, string Query, bool _search, string filters)
        {
            var jsonData = default(dynamic);
            List<GNM_PartyContactPersonDetails> partyContactList = new List<GNM_PartyContactPersonDetails>();
            try
            {
                int Total = 0;

                int userid = Obj.User_ID;
                int LangID = Obj.Language_ID;
                int CompanyID = Convert.ToInt32(Obj.Company_ID);
                int BranchID = Convert.ToInt32(Obj.Branch);

                int PartyIDLocal = 0;
                IQueryable<CallHistory> IQCallHistory = null;
                IEnumerable<CallHistory> IECallHistoryArray = null;

                IEnumerable<GNM_PartyContactPersonDetails> PartyContact = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {

                    string _query = @"
                    SELECT *
                    FROM 
                       GNM_PartyContactPersonDetails
                    WHERE 
                        [Language_ID] = @LanguageID";
                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@LanguageID", LangID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {

                                    GNM_PartyContactPersonDetails contact = new GNM_PartyContactPersonDetails
                                    {
                                        PartyContactPerson_ID = Convert.ToInt32(reader["PartyContactPerson_ID"]),
                                        Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                        PartyContactPerson_Name = reader["PartyContactPerson_Name"].ToString(),
                                        PartyContactPerson_Email = reader["PartyContactPerson_Email"].ToString(),
                                        PartyContactPerson_Department = reader["PartyContactPerson_Department"].ToString(),
                                        PartyContactPerson_Mobile = reader["PartyContactPerson_Mobile"].ToString(),
                                        PartyContactPerson_Phone = reader["PartyContactPerson_Phone"].ToString(),
                                        Party_IsDefaultContact = Convert.ToBoolean(reader["Party_IsDefaultContact"]),
                                        PartyContactPerson_IsActive = Convert.ToBoolean(reader["PartyContactPerson_IsActive"]),
                                        PartyContactPerson_Remarks = reader["PartyContactPerson_Remarks"].ToString(),
                                        Language_ID = Convert.ToInt32(reader["Language_ID"]),
                                        PartyContactPerson_DOB = reader["PartyContactPerson_DOB"] != DBNull.Value ? Convert.ToDateTime(reader["PartyContactPerson_DOB"]) : (DateTime?)null
                                    };

                                    partyContactList.Add(contact);
                                }
                                PartyContact = partyContactList.AsEnumerable();
                            }





                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetCallHistory party contact retrieval: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                string query = string.Empty;
                string finalquery = string.Empty;
                string wherecondition = string.Empty;
                string inCondition = string.Empty;
                int count = 0;
                var TMLPartlist = string.Empty;
                var TMLPartDesc = string.Empty;
                bool IsAddPermission = false;
                int userLanguageID = Convert.ToInt32(Obj.userLanguageID);
                int generalLanguageID = Convert.ToInt32(Obj.generalLanguageID);
                string _endStepStsName = await _utilityServiceClient.GetEndStepStatusNameAsync(await _utilityServiceClient.GetWorkFlowIDAsync("Case Registration", _configuration["DbName"], connString, LogException), connString, LogException);
                string EndStepStatusName = _endStepStsName.ToUpper();
                int Dealer = 0;
                if (Obj.IsDealer == true)
                {
                    Dealer = 1;
                }
                string CompanyType = "";
                using (SqlConnection conn = new SqlConnection(connString))
                {

                    string _query = "SELECT TOP 1 Company_Type FROM GNM_Company WHERE Company_ID = @CompanyID";
                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            var result = cmd.ExecuteScalar();

                            if (result != DBNull.Value)
                            {
                                CompanyType = result.ToString();
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetCallHistory company type retrieval: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }


                if (Obj.PartyID != "R")
                {
                    PartyIDLocal = Convert.ToInt32(Obj.PartyID);

                    if (advnce)
                    {
                        string op = "";
                        string decodedValue = Uri.UnescapeDataString(Query);
                        AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();
                        if (advnfilter.rules.Count() > 0)
                        {
                            for (int i = 0; i < advnfilter.rules.Count(); i++)
                            {
                                var _decryptedData = await _utilityServiceClient.DecryptStringAsync(advnfilter.rules.ElementAt(i).Data);
                                advnfilter.rules.ElementAt(i).Data = _decryptedData.Replace("'", "''");
                                advnfilter.rules.ElementAt(i).Data = advnfilter.rules.ElementAt(i).Data.Trim();

                                if (advnfilter.rules.ElementAt(i).Field == "RequestNumber")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "sr.ServiceRequestNumber";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "IssueArea")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "issuearea.RefMasterDetail_Name";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "Date")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "REPLACE(CONVERT(varchar(25),ServiceRequestDate,106),' ','-')";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "Status")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "st.WFStepStatus_Nm";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "Party")
                                {
                                    if (Obj.IsDealer == true)
                                    {
                                        advnfilter.rules.ElementAt(i).Field = "branchdata.Branch_Name";

                                    }
                                    else
                                    {
                                        advnfilter.rules.ElementAt(i).Field = "p.Party_Name";
                                    }
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "ContactPerson")
                                {
                                    if (Obj.IsDealer == true)
                                    {


                                    }
                                    else
                                    {
                                        advnfilter.rules.ElementAt(i).Field = "cp.PartyContactPerson_Name";
                                    }
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "ContactPersonMobile")
                                {
                                    if (Obj.IsDealer == true)
                                    {
                                        advnfilter.rules.ElementAt(i).Field = "branchdata.Branch_Mobile";

                                    }
                                    else
                                    {
                                        advnfilter.rules.ElementAt(i).Field = "cp.PartyContactPerson_Mobile";
                                    }
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "ContactPersonEmail")
                                {
                                    if (Obj.IsDealer == true)
                                    {
                                        advnfilter.rules.ElementAt(i).Field = "branchdata.Branch_Email";

                                    }
                                    else
                                    {
                                        advnfilter.rules.ElementAt(i).Field = "cp.PartyContactPerson_Email";
                                    }
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "Department")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "cp.PartyContactPerson_Department";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "Brand")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "Brand.RefMasterDetail_Name";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "ProductType")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "ProductType.ProductType_Name";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "Model")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "model.Model_Name";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "SerialNumber")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "sr.SerialNumber";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "VCNumber")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "sr.Flexi1";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "IssueSubArea")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "IssueSubArea.IssueSubArea_Description";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "FunctionGroup")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "FunctionGroup.RefMasterDetail_Name";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "CallDescription")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "sr.CallDescription";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "RootCause")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "sr.RootCause";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "ClosureType")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "ClosureType.RefMasterDetail_Name";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "PartNumber")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "prts.Parts_PartsNumber";
                                }
                                if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsNumber")
                                {
                                    TMLPartlist = " or Tmlprtslist.Parts_PartsNumber";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "PartDescription")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "prts.Parts_PartsDescription";
                                }
                                if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsDescription")
                                {
                                    TMLPartDesc = " or Tmlprtslist.Parts_PartsDescription";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "FileName")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "SRAttachmentDetail.FileName";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "NotesDescription")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "SRNotesDetail.NotesDescription";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "NotesDepartment")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "SRNotesDetail.Department";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "Name")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "SRNotesDetail.Name";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "Action")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "WFStep.WFStep_Name";
                                }
                                else if (advnfilter.rules.ElementAt(i).Field == "AssignedTo")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "Case(c1.Addresse_Flag) when 0 then wfr.WFRole_Name else un.User_Name END";
                                }
                                op = getoperator(advnfilter.rules.ElementAt(i).Operator);

                                if (i == 0)
                                {
                                    if (op == "like")
                                    {
                                        if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsNumber")
                                        {
                                            wherecondition = wherecondition + " AND ((" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'" + TMLPartlist + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%')";
                                        }
                                        else if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsDescription")
                                        {
                                            wherecondition = wherecondition + " AND ((" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'" + TMLPartDesc + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%')";
                                        }
                                        else
                                        {
                                            wherecondition = wherecondition + " AND (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                        }
                                    }
                                    else
                                    {
                                        if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsNumber")
                                        {
                                            wherecondition = wherecondition + " AND ((" + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'" + TMLPartlist + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "')";
                                        }
                                        else if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsDescription")
                                        {
                                            wherecondition = wherecondition + " AND ((" + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'" + TMLPartDesc + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "')";
                                        }
                                        else
                                        {
                                            wherecondition = wherecondition + " AND (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                        }
                                    }
                                }
                                else
                                {
                                    if (op == "like")
                                    {
                                        if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsNumber")
                                        {
                                            if (advnfilter.rules.ElementAt(i).Condition.ToUpper() == "AND")
                                            {
                                                wherecondition = wherecondition + " AND (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'" + TMLPartlist + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%')";
                                            }
                                            else if (advnfilter.rules.ElementAt(i).Condition.ToUpper() == "OR")
                                            {
                                                wherecondition = wherecondition + " OR (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'" + TMLPartlist + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%')";
                                            }
                                        }
                                        else if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsDescription")
                                        {
                                            if (advnfilter.rules.ElementAt(i).Condition.ToUpper() == "AND")
                                            {
                                                wherecondition = wherecondition + " AND (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'" + TMLPartDesc + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%')";
                                            }
                                            else if (advnfilter.rules.ElementAt(i).Condition.ToUpper() == "OR")
                                            {
                                                wherecondition = wherecondition + " OR (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'" + TMLPartDesc + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%')";
                                            }
                                        }
                                        else
                                        {
                                            if (advnfilter.rules.ElementAt(i).Condition.ToUpper() == "AND")
                                            {
                                                wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                            }
                                            else if (advnfilter.rules.ElementAt(i).Condition.ToUpper() == "OR")
                                            {
                                                wherecondition = wherecondition + " OR " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                            }
                                            else
                                            {
                                                wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsNumber")
                                        {
                                            wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'" + TMLPartlist + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "')";
                                        }
                                        else if (advnfilter.rules.ElementAt(i).Field == "prts.Parts_PartsDescription")
                                        {
                                            wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " (" + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'" + TMLPartDesc + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "')";
                                        }
                                        else
                                        {
                                            wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                        }
                                    }
                                }
                            }
                            wherecondition = wherecondition + ")";
                        }
                    }

                    if (generalLanguageID == userLanguageID)
                    {
                        query = query + "from HD_ServiceRequest sr left outer join GNM_Party p on sr.Party_ID=p.Party_ID left outer join GNM_Branch branchdata on  branchdata.Branch_ID=sr.Party_ID left outer join GNM_RefMasterDetail issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";
                        query = query + " left outer join GNM_Model model on sr.Model_ID=model.Model_ID";
                        query = query + " left outer join GNM_RefMasterDetail Brand on sr.Brand_ID=Brand.RefMasterDetail_ID left outer join GNM_ProductType ProductType on sr.ProductType_ID=ProductType.ProductType_ID left outer join GNM_RefMasterDetail FunctionGroup on sr.FunctionGroup_ID=FunctionGroup.RefMasterDetail_ID left outer join GNM_RefMasterDetail ClosureType on sr.ClosureType_ID=ClosureType.RefMasterDetail_ID";
                        query = query + " left outer join HD_ServiceRequestPartsList SRPart on sr.ServiceRequest_ID = SRPart.ServiceRequest_ID left outer join GNM_Parts prts on SRPart.Parts_ID = prts.Parts_ID left outer join HD_CRERCREPOSITORY TMLPart on sr.ServiceRequest_ID = TMLPart.ServiceRequest_ID left outer join GNM_Parts Tmlprtslist on TMLPart.Parts_ID = Tmlprtslist.Parts_ID left outer join HD_ServiceRequestNotesDetail SRNotesDetail on sr.ServiceRequest_ID = SRNotesDetail.ServiceRequest_ID left outer join HD_ServiceRequestAttachmentInfo SRAttachmentDetail on sr.ServiceRequest_ID = SRAttachmentDetail.ServiceRequest_ID";
                        query = query + " left outer join (SELECT Addresse_ID,Transaction_ID,Addresse_Flag,WFSteps_ID FROM GNM_WFCase_Progress where WFCaseProgress_ID in (select MAX(WFCaseProgress_ID)from GNM_WFCase_Progress group by Transaction_ID) ) as c1 on sr.ServiceRequest_ID=c1.Transaction_ID left outer join GNM_WFRole wfr on c1.Addresse_ID=wfr.WFRole_ID left outer join GNM_User un on c1.Addresse_ID=un.User_ID left outer join GNM_WFSteps WFStep on c1.WFSteps_ID=WFStep.WFSteps_ID";
                        query = query + " left outer join GNM_WFStepStatus st on sr.CallStatus_ID = st.WFStepStatus_ID";
                        query = query + " left outer join HD_IssueSubArea IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID left outer join GNM_PartyContactPersonDetails cp on sr.PartyContactPerson_ID=cp.PartyContactPerson_ID";
                        if (PartyIDLocal > 0)
                        {
                            if (CompanyType == "M")
                            {
                                query = query + "  where   sr.IsDealer=" + Dealer + " and sr.Party_ID=" + PartyIDLocal + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                            }
                            else
                            {
                                query = query + " where  sr.IsDealer=" + Dealer + " and sr.Party_ID=" + PartyIDLocal + " and sr.Company_ID=" + CompanyID + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                            }
                        }
                        //Modified By Puneeth M On 10-Jan-2015 for QA Issue -->Enter name has ADDI under Customer Details and search once records are displayed under Call History click on Refresh button, now place cursor in Case # field under Call History and press Enter
                        else if (advnce)
                        {
                            if (CompanyType == "M")
                            {
                                query = query + " where " + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                            }
                            else
                            {
                                query = query + " where sr.Company_ID=" + CompanyID + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                            }
                        }
                        else if (PartyIDLocal == 0 && _search)
                        {
                            if (CompanyType == "M")
                            {
                                query = query + " where  sr.IsDealer=" + Dealer + " and  sr.Party_ID=" + PartyIDLocal + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                            }
                            else
                            {
                                query = query + " where and sr.IsDealer=" + Dealer + " and  sr.Party_ID=" + PartyIDLocal + " and sr.Company_ID=" + CompanyID + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                            }
                        }
                        //                            
                        else
                        {
                            if (CompanyType == "M")
                            {
                                query = query + " where " + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                            }
                            else
                            {
                                query = query + " where  sr.IsDealer=" + Dealer + " and  sr.Company_ID=" + CompanyID + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                            }
                        }
                    }
                    else
                    {
                        query = query + "from HD_ServiceRequest sr left outer join GNM_PartyLocale p on sr.Party_ID=p.Party_ID left outer join GNM_Branch branchdata on  branchdata.Branch_ID=sr.Party_ID and p.Language_ID=" + LangID + " left outer join GNM_RefMasterDetailLocale issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID and issuearea.Language_ID=" + LangID;
                        query = query + " left outer join GNM_ModelLocale model on sr.Model_ID=model.Model_ID left outer join GNM_WFStepStatusLocale st on sr.CallStatus_ID = st.WFStepStatus_ID";
                        query = query + " left outer join HD_IssueSubAreaLocale IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID left outer join GNM_PartyContactPersonDetails cp on sr.PartyContactPerson_ID=cp.PartyContactPerson_ID left outer join GNM_PartyContactLocale pc on cp.PartyContactPerson_ID=pc.PartyContactPerson_ID ";
                        if (PartyIDLocal > 0)
                        {
                            query = query + " where sr.Party_ID=" + PartyIDLocal + " and sr.IsDealer=" + Dealer + " and sr.Company_ID=" + CompanyID + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                        }
                        else
                        {
                            query = query + " where  sr.IsDealer=" + Dealer + " and sr.Company_ID=" + CompanyID + ((wherecondition == string.Empty) ? ("") : (" " + wherecondition));
                        }
                    }
                    finalquery = string.Empty;
                    finalquery = "select count(distinct (sr.ServiceRequest_ID))" + query;
                    List<int> rcountlist = new List<int>();
                    IEnumerable<int> rcount = null;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(finalquery, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.CommandTimeout = 600;



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        int resultCount = reader.GetInt32(0);
                                        rcountlist.Add(resultCount);
                                    }
                                    rcount = rcountlist.AsEnumerable();
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetCallHistory count query: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    count = rcount.ElementAt(0);
                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    //---To get page wise queue data with respect to given filter and search----
                    finalquery = string.Empty;
                    finalquery = ";WITH NewT AS(select distinct (sr.ServiceRequest_ID) as ServiceRequestID,ServiceRequestNumber as RequestNumber,ServiceRequestDate as Date,";
                    finalquery = finalquery + "Model_Name as Model,SerialNumber as SerialNumber,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea_Description as IssueSubArea,case when sr.IsDealer=1 then '' else  cp.PartyContactPerson_Name end as ContactPerson, case when sr.IsDealer=1 then Branch_Mobile else PartyContactPerson_Mobile end as ContactPersonMobile,WFStepStatus_Nm as Status ";
                    finalquery = finalquery + query;
                    finalquery = finalquery + ") SELECT * FROM NewT";
                    List<CallHistory> callHis = new List<CallHistory>();
                    IEnumerable<CallHistory> ServiceReq = null;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(finalquery, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.CommandTimeout = 600;



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        CallHistory callHistory = new CallHistory
                                        {
                                            ServiceRequestID = int.Parse(reader["ServiceRequestID"].ToString()),
                                            RequestNumber = reader["RequestNumber"] as string,
                                            Date = DateTime.Parse(reader["Date"].ToString()),
                                            Model = reader["Model"] as string,
                                            SerialNumber = reader["SerialNumber"] as string,
                                            IssueArea = reader["IssueArea"] as string,
                                            IssueSubArea = reader["IssueSubArea"] as string,
                                            ContactPerson = reader["ContactPerson"] as string,
                                            ContactPersonMobile = reader["ContactPersonMobile"] as string,
                                            Status = reader["Status"] as string
                                        };
                                        callHis.Add(callHistory);
                                    }
                                    ServiceReq = callHis.AsEnumerable();
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetCallHistory queue data retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }


                    //----------To store Transaction ID of respective Queue for Navigation---//
                    finalquery = string.Empty;
                    finalquery = "select distinct (sr.ServiceRequest_ID) as ServiceRequestID,ServiceRequestNumber as RequestNumber,ServiceRequestDate as Date,";
                    finalquery = finalquery + "Model_Name as Model,SerialNumber as SerialNumber,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea_Description as IssueSubArea,case when sr.IsDealer=1 then ''else cp.PartyContactPerson_Name end as ContactPerson,case when sr.IsDealer=1 then Branch_Mobile else PartyContactPerson_Mobile end as ContactPersonMobile,WFStepStatus_Nm as Status ";
                    finalquery = finalquery + query;
                    List<CallHistory> RowIndsList = new List<CallHistory>();
                    IEnumerable<CallHistory> RowInds = null;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(finalquery, conn))
                            {
                                cmd.CommandType = CommandType.Text;



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        CallHistory callHistory = new CallHistory
                                        {
                                            ServiceRequestID = int.Parse(reader["ServiceRequestID"].ToString()),
                                            RequestNumber = reader["RequestNumber"] as string,
                                            Date = DateTime.Parse(reader["Date"].ToString()),
                                            Model = reader["Model"] as string,
                                            SerialNumber = reader["SerialNumber"] as string,
                                            IssueArea = reader["IssueArea"] as string,
                                            IssueSubArea = reader["IssueSubArea"] as string,
                                            ContactPerson = reader["ContactPerson"] as string,
                                            ContactPersonMobile = reader["ContactPersonMobile"] as string,
                                            Status = reader["Status"] as string
                                        };
                                        RowIndsList.Add(callHistory);
                                    }
                                    RowInds = callHis.AsEnumerable();
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetCallHistory transaction ID retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    //Session["ServiceRequestRowInd"] = RowInds.ToList();

                    IECallHistoryArray = from SR in ServiceReq
                                         select new CallHistory()
                                         {
                                             ServiceRequestID = SR.ServiceRequestID,
                                             RequestNumber = SR.RequestNumber,
                                             Date = SR.Date,
                                             Model = SR.Model,
                                             SerialNumber = SR.SerialNumber,
                                             IssueArea = SR.IssueArea,
                                             IssueSubArea = SR.IssueSubArea,
                                             ContactPerson = SR.ContactPerson,
                                             ContactPersonMobile = SR.ContactPersonMobile,
                                             Status = SR.Status,

                                         };

                    // Convert to array for utility service calls
                    var callHistoryArray = IECallHistoryArray.ToArray();

                    // Apply filtering if search is enabled
                    if (_search)
                    {
                        string decodedValue = Uri.UnescapeDataString(filters);
                        string decryptedValue = await _utilityServiceClient.DecryptStringAsync(decodedValue);
                        Filters? filtersObj = JObject.Parse(decryptedValue).ToObject<Filters>();
                        if (filtersObj != null && filtersObj.rules.Count() > 0)
                        {
                            var filterResult = await _utilityServiceClient.FilterSearchAsync(filtersObj, callHistoryArray.Cast<object>().ToArray());
                            if (filterResult.Success)
                            {
                                callHistoryArray = filterResult.Data.Cast<CallHistory>().ToArray();
                            }
                        }
                    }

                    // Check permissions
                    IsAddPermission = await CheckAddPermissionsAsync("HelpDeskUserLandingPage", "Case Registration", _configuration["DbName"] ?? string.Empty, Obj.Company_ID, LogException, Obj.User_ID, connString);

                    // Apply ordering if specified
                    if (!string.IsNullOrEmpty(sidx) && !string.IsNullOrEmpty(sord))
                    {
                        var orderResult = await _utilityServiceClient.OrderByFieldAsync(sidx, sord, callHistoryArray.Cast<object>().ToArray());
                        if (orderResult.Success)
                        {
                            callHistoryArray = orderResult.Data.Cast<CallHistory>().ToArray();
                        }
                    }

                    // Convert back to queryable for final processing
                    IQCallHistory = callHistoryArray.AsQueryable();

                    Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    // Get the reopen text once to avoid multiple async calls
                    string reopenText = await _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "reopen");

                    // Apply pagination manually
                    var pagedData = IQCallHistory.AsEnumerable()
                        .Skip((page - 1) * rows)
                        .Take(rows)
                        .Select(a => new
                        {
                            ServiceRequestID = a.ServiceRequestID,
                            RequestNumber = "<span key='" + a.ServiceRequestID + "' style='cursor:pointer'  Rmode='GroupQ' class='LabelClick'>" + a.RequestNumber + "</span>",
                            Date = Convert.ToDateTime(a.Date).ToString("dd-MMM-yyyy"),
                            Model = a.Model,
                            SerialNumber = a.SerialNumber,
                            IssueArea = a.IssueArea,
                            IssueSubArea = a.IssueSubArea,
                            ContactPerson = a.ContactPerson,
                            ContactPersonMobile = a.ContactPersonMobile,
                            Status = a.Status,
                            Reopen = IsAddPermission == false ?
                                (a.Status.ToUpper() == EndStepStatusName) ?
                                    "<input type='button' disabled='disabled' id='" + a.RequestNumber + "' value='" + reopenText + "' key='" + a.ServiceRequestID + "' class='reopenClick' >" : "" :
                                (a.Status.ToUpper() == EndStepStatusName) ?
                                    "<input type='button' id='" + a.RequestNumber + "' value='" + reopenText + "' key='" + a.ServiceRequestID + "' class='reopenClick' >" : ""
                        }).ToList();

                    jsonData = new
                    {
                        total = Total,
                        page = page,
                        data = pagedData,
                        records = count
                    };
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in GetCallHistory: {Message}", ex.Message);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion

        // Export function Left please add it later.

        #region validateCalldateandPCD vinay n 13/11/24
        public async Task<IActionResult> ValidateCalldateandPCDAsync(validateCalldateandPCDList Obj)
        {
            int count = 0;

            if (Obj.PCD < Obj.Calldate)
            {
                count = 1;
            }

            await Task.Delay(1); // Simulate async operation for consistency
            return new JsonResult(count);
        }
        #endregion

        #region ::: CheckBayWorkshopAvailability vinay n 13/11/24:::
        /// <summary>
        /// To check bay and workshop availability
        /// </summary>
        public async Task<IActionResult> CheckBayWorkshopAvailabilityAsync(CheckBayWorkshopAvailabilityList Obj, string connString, int LogException)
        {
            int AvailableStatus = 0;
            int Branch_ID = Convert.ToInt32(Obj.Branch);

            try
            {
                using (var SQLConn = new SqlConnection(connString))
                {
                    if (SQLConn.State == ConnectionState.Closed || SQLConn.State == ConnectionState.Broken)
                    {
                        await SQLConn.OpenAsync();
                        string Query = "DECLARE @AvailableStatus int exec @AvailableStatus= Up_CheckBayWorkshopAvailability @Branch_ID,@ExpectedArrivalDate,@ExpectedDepartureDate,@IsWIPBay,@BookingMinutes,@ServiceRequest_ID,@Quotation_ID SELECT @AvailableStatus";
                        using (var SQLCmd = new SqlCommand(Query, SQLConn))
                        {
                            SQLCmd.CommandType = CommandType.Text;
                            SQLCmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                            SQLCmd.Parameters.AddWithValue("@ExpectedArrivalDate", Obj.ExpectedArrivalDate);
                            SQLCmd.Parameters.AddWithValue("@ExpectedDepartureDate", Obj.ExpectedDepartureDate);
                            SQLCmd.Parameters.AddWithValue("@IsWIPBay", Obj.IsWIPBay);
                            SQLCmd.Parameters.AddWithValue("@BookingMinutes", Obj.BookingMinutes);
                            SQLCmd.Parameters.AddWithValue("@ServiceRequest_ID", Obj.ServiceRequest_ID);
                            SQLCmd.Parameters.AddWithValue("@Quotation_ID", Obj.Quotation_ID);
                            var result = await SQLCmd.ExecuteScalarAsync();
                            AvailableStatus = Convert.ToInt32(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in CheckBayWorkshopAvailability: {Message}", ex.Message);
                }
            }
            return new JsonResult(AvailableStatus);
        }
        #endregion

        #region GetOpenCampaignDetails vinay n 20/11/24
        /// <summary>
        /// GetOpenCampaignDetails
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="sidx"></param>
        /// <param name="rows"></param>
        /// <param name="page"></param>
        /// <param name="sord"></param>
        /// <param name="_search"></param>
        /// <param name="nd"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="Query"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetOpenCampaignDetailsAsync(GetOpenCampaignDetailsList Obj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string connString, int LogException)
        {
            var jsondata = default(dynamic);
            GNM_Product productdata = new GNM_Product();

            try
            {

                string _query = "SELECT TOP (1) * FROM GNM_Product WHERE Product_ID = @Product_ID";
                List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("@Product_ID", Obj.Product_ID)
                };
                productdata = GetValueFromDB<GNM_Product>(_query, parameters, connString);
                DateTime CurrentDateTime = DateTime.Now.Date;
                int CompanyID = Convert.ToInt32(Obj.Company_ID);
                var jsonResponse = default(dynamic);




                IEnumerable<GNM_PartyContactPersonDetails> PartyContactDetails = null;
                IEnumerable<GNM_ProductCustomer> ProdCustomer = null;
                List<GNM_ProductCustomer> productCustomersList = new List<GNM_ProductCustomer>();
                string query1 = "select * from GNM_ProductCustomer where Product_ID=" + Obj.Product_ID + " and  (getdate() between ProductCustomer_FromDate and isnull(ProductCustomer_ToDate,getdate()))";

                productCustomersList = GetValueFromDB<List<GNM_ProductCustomer>>(query1, null, connString);

                ProdCustomer = productCustomersList.AsEnumerable();
                int party_ID = Convert.ToInt32(ProdCustomer.ElementAt(0).Party_ID);


                List<SRM_Campaign> CampaignWithOEM = new List<SRM_Campaign>();
                string SelCampa = "select * from SRM_Campaign CAM join SRM_CampaignMachineDetails CMD on CAM.Campaign_ID = CMD.Campaing_ID where (CMD.JobCard_ID is NULL or CMD.JobCard_ID = 0) and CAM.Model_ID = " + productdata.Model_ID + " and CAM.IsActive = 1 and CAM.ProductType_ID = " + productdata.ProductType_ID + " and CMD.SerialNumber = '" + productdata.Product_SerialNumber + "' and CAM.StartDate <= '" + CurrentDateTime + "' and CAM.EndDate >= '" + CurrentDateTime + "'";
                CampaignWithOEM = GetValueFromDB<List<SRM_Campaign>>(SelCampa, null, connString);


                IEnumerable<ParentCompanyObject> ParentCompanyForCam = null;
                string parentCompanyQuery = @"
                    -- Create a temporary table to store results
                    CREATE TABLE #ParentCompanyTemp (
                        Company_ID INT,
                        Company_Name NVARCHAR(255),
                        Company_Parent_ID INT
                    );
 
                    -- Insert the base case (the initial company)
                    INSERT INTO #ParentCompanyTemp
                    SELECT [Company_ID], [Company_Name], [Company_Parent_ID]
                    FROM GNM_Company
                    WHERE [Company_ID] = @CompanyID;
 
                    -- Declare a variable to track the number of iterations
                    DECLARE @RowCount INT;
 
                    -- Continue iterating to fetch child companies
                    SET @RowCount = 1; -- Initial row count
                    WHILE @RowCount > 0
                    BEGIN
                        -- Insert child companies that have not been added yet
                        INSERT INTO #ParentCompanyTemp
                        SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID]
                        FROM GNM_Company AS child
                        JOIN #ParentCompanyTemp AS parent ON child.[Company_Parent_ID] = parent.[Company_ID]
                        WHERE NOT EXISTS (
                            SELECT 1 FROM #ParentCompanyTemp AS temp WHERE temp.[Company_ID] = child.[Company_ID]
                        );
 
                        -- Get the number of rows inserted
                        SET @RowCount = @@ROWCOUNT;
                    END
 
                    -- Now select the data from the temp table
                    SELECT [Company_ID], [Company_Name], [Company_Parent_ID]
                    FROM #ParentCompanyTemp;
 
                    -- Clean up the temporary table
                    DROP TABLE #ParentCompanyTemp;
                ";
                List<SqlParameter> parametersParentCamp = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyID", CompanyID)
                };
                List<ParentCompanyObject> parentCompanies = new List<ParentCompanyObject>();
                parentCompanies = GetValueFromDB<List<ParentCompanyObject>>(parentCompanyQuery, parametersParentCamp, connString);

                ParentCompanyForCam = parentCompanies.AsEnumerable();
                List<SRM_Campaign> CampaignMachineDetail = new List<SRM_Campaign>();
                CampaignMachineDetail = (from a in CampaignWithOEM
                                         join b in ParentCompanyForCam
                                         on a.Company_ID equals b.Company_ID
                                         select a).ToList();


                List<CampaignClass> CampaignHdr = new List<CampaignClass>();
                List<CampaignClass> CampaignDetails = new List<CampaignClass>();

                string OperationAndServicequery = " select distinct Campaign_ID from SRM_CampaignPartsDetails  where Campaign_ID not in (select distinct Campaign_ID  from SRM_CampaignServiceChargeDetails  union  select distinct Campaing_ID from SRM_CampaignOperationDetails) ";



                string query = "EXEC HDUp_CampaignSelection '" + productdata.Product_SerialNumber + "',0";
                CampaignDetails = GetValueFromDB<List<CampaignClass>>(query, null, connString);





                string OnlypartCampignquery = "  select SC.Campaign_ID,SC.CampaignName,SC.CampaignCode,SC.Company_ID from SRM_Campaign SC  join SRM_CampaignMachineDetails SM on SC.Campaign_ID=SM.Campaing_ID ";
                OnlypartCampignquery += " where  SM.SerialNumber='" + productdata.Product_SerialNumber + "'   and sc.StartDate <= '" + DateTime.Now.Date + "' and SC.IsActive=1 and sc.EndDate >= '" + DateTime.Now.Date + "'" + " and Campaign_ID in(" + OperationAndServicequery + ")";
                List<CampaignClass> CampaignDetailsRange = new List<CampaignClass>();
                CampaignDetailsRange = GetValueFromDB<List<CampaignClass>>(OnlypartCampignquery, null, connString);
                CampaignDetails.AddRange(CampaignDetailsRange);


                CampaignHdr = (from a in CampaignDetails
                               join b in ParentCompanyForCam
                                   on a.Company_ID equals b.Company_ID
                               select a).ToList();

                var CampaignCodeArray = (from a in CampaignDetails
                                         orderby a.CampaignName
                                         select new
                                         {

                                             CampaignCode = a.CampaignCode,
                                             CampaignName = a.CampaignName,
                                         }).Distinct().ToList();
                bool IsUnderCampign = false;
                IsUnderCampign = CampaignDetails.Count() > 0 ? true : false;
                int Total = CampaignCodeArray.Count;
                jsondata = new
                {
                    total = Total,
                    page = page,
                    records = Total,
                    data = CampaignCodeArray,
                    IsUnderCampign,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in GetOpenCampaignDetails: {Message}", ex.Message);
                }
                return null;
            }
            return new JsonResult(jsondata);
        }
        #endregion

        #region GetInitialData vinay n 20/11/24
        public async Task<IActionResult> GetInitialData(GetInitialDataList Obj, string connString, int LogException)
        {
            var JsonResult = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);

                List<GNM_RefMaster> refMaster = new List<GNM_RefMaster>();
                using (SqlConnection conn = new SqlConnection(connString))
                {

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand("SELECT * FROM GNM_RefMaster", conn))
                        {
                            cmd.CommandType = CommandType.Text;


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {

                                    GNM_RefMaster REF = new GNM_RefMaster
                                    {
                                        RefMaster_Name = reader["RefMaster_Name"] as string

                                    };
                                    refMaster.Add(REF);
                                }

                            }





                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetInitialData ref master retrieval: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();
                using (SqlConnection conn = new SqlConnection(connString))
                {

                    SqlCommand cmd = null;
                    string _query = @"
                        SELECT *
                        FROM GNM_RefMasterDetail;";

                    try
                    {
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {

                                    refDetail.Add(new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_IsActive = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_IsActive")) ? false : reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive")),
                                        RefMasterDetail_Short_Name = reader["RefMasterDetail_Short_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Short_Name"].ToString(),
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Name"].ToString(),
                                        Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        ModifiedBy = reader.IsDBNull(reader.GetOrdinal("ModifiedBy")) ? 0 : reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                        ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                        RefMaster_ID = reader.IsDBNull(reader.GetOrdinal("RefMaster_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                        RefMasterDetail_IsDefault = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_IsDefault")) ? false : reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsDefault")),
                                        Region_ID = reader.IsDBNull(reader.GetOrdinal("Region_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Region_ID")),
                                        SystemCondition = reader["SystemCondition"] == DBNull.Value ? null : reader["SystemCondition"].ToString()
                                    });

                                }

                            }





                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetInitialData ref detail retrieval: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                List<GNM_RefMasterDetailLocale> refDetailLocale = new List<GNM_RefMasterDetailLocale>();
                using (SqlConnection conn = new SqlConnection(connString))
                {

                    SqlCommand cmd = null;
                    string _query = @"
                        SELECT *
                        FROM GNM_RefMasterDetailLocale;";

                    try
                    {
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {

                                    refDetailLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetailLocale_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetailLocale_ID")),
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                        RefMasterDetail_Short_Name = reader["RefMasterDetail_Short_Name"] as string,
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"] as string,
                                        Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID"))
                                    });
                                }

                            }





                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetInitialData ref detail locale retrieval: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                int CompanyID = Convert.ToInt32(Obj.Company_ID);
                var Search = default(dynamic);
                GNM_CompParam CP = new GNM_CompParam();
                using (SqlConnection conn = new SqlConnection(connString))
                {

                    SqlCommand cmd = null;
                    string _query = @"
                        SELECT TOP 1
                            CompanyParam_ID,
                            Company_ID,
                            Param_Name,
                            Param_value
                        FROM GNM_CompParam
                        WHERE UPPER(Param_Name) = 'RELATEDISSUESEARCHCRITERIA' AND Company_ID = @CompanyID;";

                    try
                    {
                        using (cmd = new SqlCommand(_query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.Add(new SqlParameter("@CompanyID", SqlDbType.Int) { Value = Obj.Company_ID });



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    CP = new GNM_CompParam
                                    {
                                        CompanyParam_ID = reader.GetInt32(reader.GetOrdinal("CompanyParam_ID")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Param_Name = reader["Param_Name"] as string,
                                        Param_value = reader["Param_value"] as string
                                    };
                                }

                            }





                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            _logger.LogError(ex, "Error in GetInitialData company param retrieval: {Message}", ex.Message);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                string Query = string.Empty;
                List<TableDescription> Result = new List<TableDescription>();
                List<TableDescription> TempResult = new List<TableDescription>();
                string PackingTypeNames = "0:------" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "select").ToString() + "------;";

                if (CP != null)
                {
                    string[] Filter = CP.Param_value.Split(',');
                    for (int i = 0; i < Filter.Length; i++)
                    {
                        TableDescription Temp = new TableDescription();
                        Query = "select sys.extended_properties.value as Label,sys.types.name as DataType,sys.columns.name as ColumnName,'' as ControlID from sys.columns inner join sys.types on sys.columns.system_type_id=sys.types.system_type_id and sys.columns.name in ('" + Filter[i] + "') inner join sys.extended_properties on sys.extended_properties.major_id=sys.columns.object_id and sys.extended_properties.minor_id=sys.columns.column_id and sys.extended_properties.name='MS_Description' and sys.types.name <> 'sysname' inner join sys.tables on sys.columns.object_id = sys.tables.object_id and tables.name in ('HD_CRERCRepository','HD_ServiceRequest')";
                        TempResult.Clear();
                        using (SqlConnection conn = new SqlConnection(connString))
                        {

                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(Query, conn))
                                {
                                    cmd.CommandType = CommandType.Text;


                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {

                                            TempResult.Add(new TableDescription
                                            {
                                                Label = reader["Label"] as string,
                                                DataType = reader["DataType"] as string,
                                                ColumnName = reader["ColumnName"] as string,
                                                ControlID = reader["ControlID"] as string
                                            });
                                        }

                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetInitialData table description retrieval: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }

                        Temp = TempResult[0];

                        Query = "select '' as  Label,sys.types.name as DataType,sys.columns.name as ColumnName,sys.extended_properties.value as ControlID from sys.columns inner join sys.types on sys.columns.system_type_id=sys.types.system_type_id and sys.columns.name in ('" + Filter[i] + "')inner join sys.extended_properties on sys.extended_properties.major_id=sys.columns.object_id and sys.extended_properties.minor_id=sys.columns.column_id and sys.extended_properties.name='ControlName' and sys.types.name <> 'sysname' inner join sys.tables on sys.columns.object_id = sys.tables.object_id and tables.name in ('HD_CRERCRepository','HD_ServiceRequest')";
                        TempResult.Clear();
                        using (SqlConnection conn = new SqlConnection(connString))
                        {

                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(Query, conn))
                                {
                                    cmd.CommandType = CommandType.Text;


                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {

                                            TableDescription description = new TableDescription
                                            {
                                                Label = reader["Label"] as string,
                                                DataType = reader["DataType"] as string,
                                                ColumnName = reader["ColumnName"] as string,
                                                ControlID = reader["ControlID"] as string
                                            };
                                            TempResult.Add(description);
                                        }

                                    }





                                }


                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    _logger.LogError(ex, "Error in GetInitialData table description retrieval: {Message}", ex.Message);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }

                        }

                        Temp.ControlID = TempResult[0].ControlID;
                        Result.Add(Temp);
                    }
                    Search = Result;
                }
                var FollowUpModes = default(dynamic);
                var FollowStatus = default(dynamic);
                if (userLanguageID == generalLanguageID)
                {
                    FollowUpModes = from a in refMaster
                                    join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                    where a.RefMaster_Name.ToUpper() == "FOLLOWUPMODE" && b.RefMasterDetail_IsActive == true
                                    select new
                                    {
                                        b.RefMasterDetail_ID,
                                        b.RefMasterDetail_Name
                                    };

                    FollowStatus = from a in refMaster
                                   join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                   where a.RefMaster_Name.ToUpper() == "FOLLOWUPSTATUS" && b.RefMasterDetail_IsActive == true
                                   select new
                                   {
                                       b.RefMasterDetail_ID,
                                       b.RefMasterDetail_Name
                                   };

                    string PackingTypeQuery = "select PackingType_ID,PackingType_Name from SLM_PackingType where IsActive=1";
                    List<PackingListObj> PackingTypeList = new List<PackingListObj>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(PackingTypeQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {

                                        PackingListObj packingListObj = new PackingListObj
                                        {
                                            PackingType_ID = reader["PackingType_ID"] != DBNull.Value ? Convert.ToInt32(reader["PackingType_ID"]) : 0,
                                            PackingType_Name = reader["PackingType_Name"] as string
                                        };
                                        PackingTypeList.Add(packingListObj);
                                    }

                                }





                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetInitialData packing type retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    foreach (var obj in PackingTypeList)
                    {
                        PackingTypeNames = PackingTypeNames + obj.PackingType_ID + ":" + obj.PackingType_Name + ";";
                    }
                    PackingTypeNames = PackingTypeNames.TrimEnd(new char[] { ';' });
                }
                else
                {
                    FollowUpModes = from a in refMaster
                                    join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                    join c in refDetailLocale on b.RefMasterDetail_ID equals c.RefMasterDetail_ID
                                    where a.RefMaster_Name.ToUpper() == "FOLLOWUPMODE" && b.RefMasterDetail_IsActive == true && c.Language_ID == userLanguageID
                                    select new
                                    {
                                        c.RefMasterDetail_ID,
                                        c.RefMasterDetail_Name
                                    };

                    FollowStatus = from a in refMaster
                                   join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                   join c in refDetailLocale on b.RefMasterDetail_ID equals c.RefMasterDetail_ID
                                   where a.RefMaster_Name.ToUpper() == "FOLLOWUPSTATUS" && b.RefMasterDetail_IsActive == true && c.Language_ID == userLanguageID
                                   select new
                                   {
                                       c.RefMasterDetail_ID,
                                       c.RefMasterDetail_Name
                                   };

                    string PackingTypeQuery = "select B.PackingType_ID,B.PackingType_Name from SLM_PackingType A join SLM_PackingTypeLocale B on A.PackingType_ID=B.PackingType_ID where IsActive=1";
                    List<PackingListObj> PackingTypeList = new List<PackingListObj>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(PackingTypeQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {

                                        PackingListObj packingListObj = new PackingListObj
                                        {
                                            PackingType_ID = reader["PackingType_ID"] != DBNull.Value ? Convert.ToInt32(reader["PackingType_ID"]) : 0,
                                            PackingType_Name = reader["PackingType_Name"] as string
                                        };
                                        PackingTypeList.Add(packingListObj);
                                    }

                                }





                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                _logger.LogError(ex, "Error in GetInitialData packing type retrieval: {Message}", ex.Message);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    foreach (var obj in PackingTypeList)
                    {
                        PackingTypeNames = PackingTypeNames + obj.PackingType_ID + ":" + obj.PackingType_Name + ";";
                    }
                    PackingTypeNames = PackingTypeNames.TrimEnd(new char[] { ';' });
                }
                string FollowUpModeNames = "-1:--" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "select").ToString() + "--;";

                foreach (var FUMObj in FollowUpModes)
                {
                    FollowUpModeNames = FollowUpModeNames + FUMObj.RefMasterDetail_ID + ":" + FUMObj.RefMasterDetail_Name + ";";
                }
                FollowUpModeNames = FollowUpModeNames.TrimEnd(new char[] { ';' });

                string FollowUpStatusNames = "-1:--" + _utilityServiceClient.GetResourceStringAsync(Obj.UserCulture.ToString(), "select").ToString() + "--;";
                foreach (var FUSObj in FollowStatus)
                {
                    FollowUpStatusNames = FollowUpStatusNames + FUSObj.RefMasterDetail_ID + ":" + FUSObj.RefMasterDetail_Name + ";";
                }
                FollowUpStatusNames = FollowUpStatusNames.TrimEnd(new char[] { ';' });
                string query = @"SELECT Param_value 
                 FROM GNM_CompParam 
                 WHERE UPPER(Param_Name) = 'ISPARTSQUANTITYMANDATORY' AND Company_ID = @CompanyID";
                List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyID", CompanyID)
                };
                string paramValue = GetValFromDB(query, parameters, connString);

                bool IsPartsQuantityMandatory = string.IsNullOrEmpty(paramValue) || paramValue.ToUpper() == "TRUE";
                string queryISROOTCAUSEMANDATORY = @"SELECT Param_value 
                 FROM GNM_CompParam 
                 WHERE UPPER(Param_Name) = 'ISROOTCAUSEMANDATORY' AND Company_ID = @CompanyID";
                List<SqlParameter> parametersISROOTCAUSEMANDATORY = new List<SqlParameter>
                    {
                        new SqlParameter("@CompanyID", CompanyID)
                    };


                bool ISROOTCAUSEMANDATORY = GetBooleanValueFromDB(queryISROOTCAUSEMANDATORY, parametersISROOTCAUSEMANDATORY, connString);
                string queryISFUNCTIONGROUPMANDATORY = @"SELECT Param_value 
                 FROM GNM_CompParam 
                 WHERE UPPER(Param_Name) = 'ISFUNCTIONGROUPMANDATORY' AND Company_ID = @CompanyID";
                List<SqlParameter> parametersISFUNCTIONGROUPMANDATORY = new List<SqlParameter>
                    {
                        new SqlParameter("@CompanyID", CompanyID)
                    };



                bool ISFUNCTIONGROUPMANDATORY = GetBooleanValueFromDB(queryISFUNCTIONGROUPMANDATORY, parametersISFUNCTIONGROUPMANDATORY, connString);

                string queryCHANGESERIALNUMTOVCNUM = @"SELECT Param_value 
                 FROM GNM_CompParam 
                 WHERE UPPER(Param_Name) = 'CHANGESERIALNUMTOVCNUM' AND Company_ID = @CompanyID";
                List<SqlParameter> parametersCHANGESERIALNUMTOVCNUM = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyID", CompanyID)
                };


                bool CHANGESERIALNUMTOVCNUM = GetBooleanValueFromDB(queryCHANGESERIALNUMTOVCNUM, parametersCHANGESERIALNUMTOVCNUM, connString);
                string queryHelpDeskOptions = @"SELECT Param_value 
                 FROM GNM_CompParam 
                 WHERE UPPER(Param_Name) = 'HELPDESKOPTIONS' AND Company_ID = @CompanyID";
                List<SqlParameter> parametersHelpDeskOptions = new List<SqlParameter>
                    {
                        new SqlParameter("@CompanyID", CompanyID)
                    };



                string HelpDeskOptions = GetValFromDB(queryHelpDeskOptions, parametersHelpDeskOptions, connString);
                if (string.IsNullOrEmpty(HelpDeskOptions))
                {
                    HelpDeskOptions = "1,2,3,4";
                }
                ;
                JsonResult = new
                {
                    FollowUpModeNames,
                    FollowUpStatusNames,
                    PackingTypeNames,
                    Search,
                    Tabs = await GetTabsDataAsync(Obj.Company_ID, connString, LogException),
                    IsPartsQuantityMandatory = IsPartsQuantityMandatory,
                    ISROOTCAUSEMANDATORY,
                    //Added By Puneeth M for TML Issue TML-2015011301(Make Function Group mandatory in case Registration.2) Defect Name need to be displayed instead of Root Cause Analysis in Call Log Report) on 22-Jan-2015
                    ISFUNCTIONGROUPMANDATORY,
                    CHANGESERIALNUMTOVCNUM,
                    //
                    HelpDeskOptions
                };
            }
            catch (Exception ex)
            {
            }
            return new JsonResult(JsonResult);
        }
        public static bool GetBooleanValueFromDB(string query, List<SqlParameter> parameters, string connectionString)
        {
            string result = GetValFromDB(query, parameters, connectionString);
            return result.ToUpper() == "TRUE";
        }
        #endregion

        public static string GetValFromDB(string query, List<SqlParameter> parameters, string connectionString)
        {
            string result = string.Empty;

            using (SqlConnection conn = new SqlConnection(connectionString)) // Ensure connectionString is defined
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    var value = cmd.ExecuteScalar(); // Execute query and get single value

                    result = value != DBNull.Value ? value.ToString() : string.Empty;
                }
            }

            return result;
        }

        public static T GetValueFromDB<T>(string query, List<SqlParameter> parameters, string connectionString)
        {
            T result = default;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    // Execute the query
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        // If the result type is a collection (e.g., List<T>)
                        if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                        {

                            var list = Activator.CreateInstance<T>();
                            var columnPropertyMapping = typeof(T).GetGenericArguments()[0]
                                    .GetProperties()
                                    .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                 .Select(reader.GetName)
                                                                 .Contains(property.Name))
                                    .Select(property => new
                                    {
                                        Property = property,
                                        Ordinal = reader.GetOrdinal(property.Name),
                                        TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                    })
                                    .ToList();
                            while (reader.Read())
                            {
                                var item = Activator.CreateInstance(typeof(T).GetGenericArguments()[0]);

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(item, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }

                              ((System.Collections.IList)list).Add(item);
                            }
                            result = (T)list;
                        }
                        // If the result is a scalar type (like string, bool, int)
                        else if (typeof(T) == typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(bool))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0].ToString().ToUpper() == "TRUE", typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(int))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        // Handle cases for custom classes/complex objects
                        else if (typeof(T).IsClass && typeof(T) != typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = Activator.CreateInstance<T>(); // Create an instance of the class
                                var columnPropertyMapping = typeof(T)
                                  .GetProperties()
                                  .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                               .Select(reader.GetName)
                                                               .Contains(property.Name))
                                  .Select(property => new
                                  {
                                      Property = property,
                                      Ordinal = reader.GetOrdinal(property.Name),
                                      TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                  })
                                  .ToList();

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(result, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }
                            }
                        }
                    }
                }
            }



            return result;
        }

        #region GetTabsData vinay n 20/11/24
        public async Task<List<TabPosition>> GetTabsDataAsync(int Company_ID, string connString, int LogException)
        {
            List<TabPosition> Tab = new List<TabPosition>();
            try
            {

                //ID: 1-Case-PartsList, 2-Case-TMLPartsList, 3-Case-Attachments, 4-Case-RelatedIssues, 5-Case-Questionnaire, 6-Case-RootCause, 7-Case-NotesDetails, 8-Case-Knowledgebase, 9-Case-FollowUps
                Tab.Add(await GetTabDetailsAsync(1, "Case-PartsList", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(2, "Case-TMLPartsList", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(3, "Case-Attachments", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(4, "Case-RelatedIssues", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(5, "Case-Questionnaire", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(6, "Case-RootCause", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(7, "Case-NotesDetails", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(8, "Case-Knowledgebase", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(9, "Case-FollowUps", Company_ID, connString));
                Tab.Add(await GetTabDetailsAsync(10, "Case-ProductDetails", Company_ID, connString));
                Tab = Tab.OrderBy(T => T.Position).ToList();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                    _logger.LogError(ex, "Error in GetTabsData: {Message}", ex.Message);
            }
            return Tab;
        }
        #endregion

        #region  vinay n 20/11/24
        public async Task<TabPosition> GetTabDetailsAsync(int ID, string Param, int Company_ID, string connString)
        {

            TabPosition Tab = new TabPosition();
            try
            {
                Tab.ID = ID;

                string query = @"
                SELECT Param_value
                FROM GNM_CompParam 
                WHERE UPPER(Param_Name) = @Param 
                AND Company_ID = @Company_ID";
                List<SqlParameter> parameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Param", Param.ToUpper()),
                        new SqlParameter("@Company_ID", Company_ID)
                    };
                string paramValue = GetValFromDB(query, parameters, connString);

                if (!string.IsNullOrEmpty(paramValue))
                {


                    Tab.Visibility = paramValue.Split(',')[0].ToUpper() == "TRUE" ? true : false;
                    Tab.Position = Int32.Parse(paramValue.Split(',')[1]);
                }
                else
                {
                    Tab.Position = ID;
                    Tab.Visibility = true;
                }
            }
            catch (Exception ex)
            {
                // Log error but continue with default values
                _logger.LogError(ex, "Error in GetTabDetails: {Message}", ex.Message);
            }
            return Tab;
        }
        #endregion

        public async Task<string> GetBranchNameAsync(string connString, int LogException, int Branch_ID)
        {
            string branchName = string.Empty;

            using (SqlConnection conn = new SqlConnection(connString))
            {
                string _query = "SELECT TOP 1 Branch_Name FROM GNM_Branch WHERE Branch_ID = @Branch_ID";

                try
                {
                    using (SqlCommand cmd = new SqlCommand(_query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            await conn.OpenAsync();
                        }

                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                branchName = reader["Branch_Name"] != DBNull.Value ? reader["Branch_Name"].ToString() ?? string.Empty : string.Empty;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        _logger.LogError(ex, "Error in GetBranchName: {Message}", ex.Message);
                    }
                }
                finally
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        await conn.CloseAsync();
                    }
                    SqlConnection.ClearAllPools();
                }
            }
            return branchName;
        }

        public void UpdateIsEditTicket(int ServiceRequest_ID, int type)
        {
            
            //type 1 - editing , 2 - saved
            try
            {
                if (ServiceRequest_ID > 0)
                {
                    HD_ServiceRequest header = null;
                    header = ServiceRequestClient.HD_ServiceRequest.Where(e => e.ServiceRequest_ID == ServiceRequest_ID).FirstOrDefault();
                    Session["EditedTicket_ID"] = ServiceRequest_ID;
                    int EditedBy = Convert.ToInt32(Session["User_ID"]);
                    if (header != null)
                    {
                        if (type == 1)
                        {
                            header.IsEdited = true;
                            header.EditedBy = EditedBy;
                        }
                        else
                        {
                            header.IsEdited = false;
                            header.EditedBy = 0;
                        }
                        ServiceRequestClient.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {

                // if (LogException == 1)
                // {
                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                _logger.LogError(ex, "Error in UpdateIsEditTicket: {Message}", ex.Message);

                // }
            }
        }
    }
}