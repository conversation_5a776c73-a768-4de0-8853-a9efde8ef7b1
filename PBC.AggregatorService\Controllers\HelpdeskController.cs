using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;
using System.Text;
using PBC.AggregatorService.DTOs;

namespace PBC.AggregatorService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HelpdeskController : ControllerBase
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<HelpdeskController> _logger;
        private readonly IConfiguration _configuration;

        public HelpdeskController(HttpClient httpClient, ILogger<HelpdeskController> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
        }

        #region ::: SelectDealerName :::
        /// <summary>
        /// Select dealer name
        /// </summary>
        /// <param name="request">Dealer name selection request</param>
        /// <returns>Dealer name selection result</returns>
        [HttpPost("select-dealer-name")]
        [Authorize]
        public async Task<IActionResult> SelectDealerName([FromBody] SelectDealerNameList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-dealer-name");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    request = request,
                    connectionString = connString
                };

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/select-dealer-name";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig.request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectDealerName");
                return StatusCode(500, "An error occurred while processing dealer name selection");
            }
        }
        #endregion

        #region ::: InitialSetup :::
        /// <summary>
        /// Get initial setup data
        /// </summary>
        /// <param name="request">Initial setup request</param>
        /// <returns>Initial setup data</returns>
        [HttpPost("initial-setup")]
        [Authorize]
        public async Task<IActionResult> InitialSetup([FromBody] InitialSetupList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/initial-setup");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/initial-setup";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InitialSetup");
                return StatusCode(500, "An error occurred while processing initial setup");
            }
        }
        #endregion

        #region ::: GetObjectID :::
        /// <summary>
        /// Get object ID by name
        /// </summary>
        /// <param name="name">Object name</param>
        /// <returns>Object ID</returns>
        [HttpGet("object-id/{name}")]
        [Authorize]
        public async Task<IActionResult> GetObjectID(string name)
        {
            try
            {
                _logger.LogInformation("GET /api/Helpdesk/object-id/{Name}", name);

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/object-id/{name}";

                var response = await _httpClient.GetAsync($"{endpoint}?connectionString={connString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetObjectID for name: {Name}", name);
                return StatusCode(500, "An error occurred while getting object ID");
            }
        }
        #endregion

        #region ::: GetBranchName :::
        /// <summary>
        /// Get branch name by ID
        /// </summary>
        /// <param name="branchId">Branch ID</param>
        /// <returns>Branch name</returns>
        [HttpGet("branch-name/{branchId}")]
        [Authorize]
        public async Task<IActionResult> GetBranchName(int branchId)
        {
            try
            {
                _logger.LogInformation("GET /api/Helpdesk/branch-name/{BranchId}", branchId);

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/branch-name/{branchId}";

                var response = await _httpClient.GetAsync($"{endpoint}?connectionString={connString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetBranchName for ID: {BranchId}", branchId);
                return StatusCode(500, "An error occurred while getting branch name");
            }
        }
        #endregion

        #region ::: GetTabsData :::
        /// <summary>
        /// Get tabs data by company ID
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Tabs data</returns>
        [HttpGet("tabs-data/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetTabsData(int companyId)
        {
            try
            {
                _logger.LogInformation("GET /api/Helpdesk/tabs-data/{CompanyId}", companyId);

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/tabs-data/{companyId}";

                var response = await _httpClient.GetAsync($"{endpoint}?connectionString={connString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetTabsData for company ID: {CompanyId}", companyId);
                return StatusCode(500, "An error occurred while getting tabs data");
            }
        }
        #endregion

        #region ::: CheckAddPermissions :::
        /// <summary>
        /// Check add permissions for user
        /// </summary>
        /// <param name="request">Permission check request</param>
        /// <returns>Permission check result</returns>
        [HttpPost("check-add-permissions")]
        [Authorize]
        public async Task<IActionResult> CheckAddPermissions([FromBody] CheckAddPermissionsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-add-permissions");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/check-add-permissions";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckAddPermissions");
                return StatusCode(500, "An error occurred while checking permissions");
            }
        }
        #endregion

        #region ::: ValidateCalldateAndPCD :::
        /// <summary>
        /// Validate call date and PCD
        /// </summary>
        /// <param name="request">Date validation request</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate-calldate-pcd")]
        [Authorize]
        public async Task<IActionResult> ValidateCalldateAndPCD([FromBody] validateCalldateandPCDList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/validate-calldate-pcd");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/validate-calldate-pcd";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateCalldateAndPCD");
                return StatusCode(500, "An error occurred while validating dates");
            }
        }
        #endregion

        #region ::: CheckBayWorkshopAvailability :::
        /// <summary>
        /// Check bay workshop availability
        /// </summary>
        /// <param name="request">Workshop availability check request</param>
        /// <returns>Availability check result</returns>
        [HttpPost("check-bay-workshop-availability")]
        [Authorize]
        public async Task<IActionResult> CheckBayWorkshopAvailability([FromBody] CheckBayWorkshopAvailabilityList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-bay-workshop-availability");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/check-bay-workshop-availability";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckBayWorkshopAvailability");
                return StatusCode(500, "An error occurred while checking workshop availability");
            }
        }
        #endregion

        #region ::: CheckForWorkshopBlockOverlap :::
        /// <summary>
        /// Check for workshop block overlap
        /// </summary>
        /// <param name="request">Workshop block overlap check request</param>
        /// <returns>Overlap check result</returns>
        [HttpPost("check-workshop-block-overlap")]
        [Authorize]
        public async Task<IActionResult> CheckForWorkshopBlockOverlap([FromBody] CheckForWorkshopBlockOverlapList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-workshop-block-overlap");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/check-workshop-block-overlap";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckForWorkshopBlockOverlap");
                return StatusCode(500, "An error occurred while checking workshop block overlap");
            }
        }
        #endregion

        #region ::: SelectFieldSearchParty :::
        /// <summary>
        /// Select field search party
        /// </summary>
        /// <param name="request">Field search party request</param>
        /// <returns>Field search party result</returns>
        [HttpPost("select-field-search-party")]
        [Authorize]
        public async Task<IActionResult> SelectFieldSearchParty([FromBody] SelectFieldSearchPartyRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-field-search-party");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/select-field-search-party";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectFieldSearchParty");
                return StatusCode(500, "An error occurred while searching parties");
            }
        }
        #endregion

        #region ::: GetCustomerData :::
        /// <summary>
        /// Get customer data
        /// </summary>
        /// <param name="request">Customer data request</param>
        /// <returns>Customer data</returns>
        [HttpPost("get-customer-data")]
        [Authorize]
        public async Task<IActionResult> GetCustomerData([FromBody] GetCustomerDataRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-customer-data");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-customer-data";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCustomerData");
                return StatusCode(500, "An error occurred while getting customer data");
            }
        }
        #endregion

        #region ::: GetDealerData :::
        /// <summary>
        /// Get dealer data
        /// </summary>
        /// <param name="request">Dealer data request</param>
        /// <returns>Dealer data</returns>
        [HttpPost("get-dealer-data")]
        [Authorize]
        public async Task<IActionResult> GetDealerData([FromBody] GetDealerDataRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-dealer-data");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-dealer-data";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDealerData");
                return StatusCode(500, "An error occurred while getting dealer data");
            }
        }
        #endregion

        #region ::: GetProductDetails :::
        /// <summary>
        /// Get product details
        /// </summary>
        /// <param name="request">Product details request</param>
        /// <returns>Product details</returns>
        [HttpPost("get-product-details")]
        [Authorize]
        public async Task<IActionResult> GetProductDetails([FromBody] GetProductDetailsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-product-details");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-product-details";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductDetails");
                return StatusCode(500, "An error occurred while getting product details");
            }
        }
        #endregion

        #region ::: CheckDuplicateContactPerson :::
        /// <summary>
        /// Check duplicate contact person
        /// </summary>
        /// <param name="request">Duplicate contact person check request</param>
        /// <returns>Duplicate check result</returns>
        [HttpPost("check-duplicate-contact-person")]
        [Authorize]
        public async Task<IActionResult> CheckDuplicateContactPerson([FromBody] CheckDuplicateContactPersonRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/check-duplicate-contact-person");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/check-duplicate-contact-person";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckDuplicateContactPerson");
                return StatusCode(500, "An error occurred while checking duplicate contact person");
            }
        }
        #endregion

        #region ::: GetOpenCampaignDetails :::
        /// <summary>
        /// Get open campaign details
        /// </summary>
        /// <param name="request">Campaign details request</param>
        /// <returns>Campaign details</returns>
        [HttpPost("get-open-campaign-details")]
        [Authorize]
        public async Task<IActionResult> GetOpenCampaignDetails([FromBody] GetOpenCampaignDetailsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-open-campaign-details");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-open-campaign-details";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetOpenCampaignDetails");
                return StatusCode(500, "An error occurred while getting campaign details");
            }
        }
        #endregion

        #region ::: GetInitialData :::
        /// <summary>
        /// Get initial data for help desk user landing page
        /// </summary>
        /// <param name="request">Initial data request</param>
        /// <returns>Initial data</returns>
        [HttpPost("get-initial-data")]
        [Authorize]
        public async Task<IActionResult> GetInitialData([FromBody] GetInitialDataList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/get-initial-data");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-initial-data";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetInitialData");
                return StatusCode(500, "An error occurred while getting initial data");
            }
        }
        #endregion

        #region ::: SelectServiceRequest :::
        /// <summary>
        /// Select service request records with pagination and filtering
        /// </summary>
        /// <param name="request">Service request selection request</param>
        /// <returns>Service request records</returns>
        [HttpPost("select-service-request")]
        [Authorize]
        public async Task<IActionResult> SelectServiceRequest([FromBody] SelectServiceRequestRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-service-request");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/select-service-request";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectServiceRequest");
                return StatusCode(500, "An error occurred while selecting service requests");
            }
        }
        #endregion

        #region ::: GetTabDetails :::
        /// <summary>
        /// Get tab details for a specific tab
        /// </summary>
        /// <param name="id">Tab ID</param>
        /// <param name="param">Parameter name</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>Tab details</returns>
        [HttpGet("get-tab-details")]
        [Authorize]
        public async Task<IActionResult> GetTabDetails([FromQuery] int id, [FromQuery] string param, [FromQuery] int companyId)
        {
            try
            {
                _logger.LogInformation("GET /api/Helpdesk/get-tab-details");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/get-tab-details?id={id}&param={param}&companyId={companyId}";

                var response = await _httpClient.GetAsync($"{endpoint}&connectionString={connString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetTabDetails");
                return StatusCode(500, "An error occurred while getting tab details");
            }
        }
        #endregion

        #region ::: SelWorkFlowSummary :::
        /// <summary>
        /// Select workflow summary data
        /// </summary>
        /// <param name="request">Workflow summary request</param>
        /// <returns>Workflow summary data</returns>
        [HttpPost("select-workflow-summary")]
        [Authorize]
        public async Task<IActionResult> SelWorkFlowSummary([FromBody] GetWorkFlowSummaryList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/select-workflow-summary");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/select-workflow-summary";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelWorkFlowSummary");
                return StatusCode(500, "An error occurred while getting workflow summary");
            }
        }
        #endregion

        #region ::: SaveCustomer :::
        /// <summary>
        /// Save customer details
        /// </summary>
        /// <param name="request">Customer save request</param>
        /// <returns>Save result</returns>
        [HttpPost("save-customer")]
        [Authorize]
        public async Task<IActionResult> SaveCustomer([FromBody] SaveCustomerList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Helpdesk/save-customer");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.HelpdeskService
                var helpdeskServiceUrl = _configuration["ServiceUrls:HelpdeskService"];
                var endpoint = $"{helpdeskServiceUrl}/api/helpdeskuserlandingpage/save-customer";

                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}?connectionString={connString}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling HelpdeskService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SaveCustomer");
                return StatusCode(500, "An error occurred while saving customer");
            }
        }
        #endregion
    }
}