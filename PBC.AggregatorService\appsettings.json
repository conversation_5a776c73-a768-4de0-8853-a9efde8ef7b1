{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=AggregatorSharedAPI;Trusted_Connection=true;MultipleActiveResultSets=true", "FSMGOLD": "Data Source=quest-dev-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCLSoftware_AMP_MicroService_GUI; User ID=sd_dev;password=**********;multipleactiveresultsets=True;"}, "ServiceUrls": {"CoreService": "http://localhost:5001", "UtilitiesService": "http://localhost:5003", "HelpdeskService": "http://localhost:5002", "WorkflowService": "http://localhost:5005"}, "ServicePorts": {"AggregatorService": "5004", "CoreService": "5001", "HelpdeskService": "5002", "UtilitiesService": "5003", "WorkflowService": "5005"}, "LogError": "1", "Jwt": {"SecretKey": "RD$46CwF5uSoYuSGhpVz9xKQovBZWjJorr/zUxMkByRP4FRE=", "Issuer": "http://localhost:5001", "Audience": "http://localhost:5001/api", "ExpiryMinutes": 60}}